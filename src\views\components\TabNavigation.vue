<template>
    <nav class="modern-navbar">
        <!-- Mobile: Drawer Toggle + Page Title -->
        <div class="mobile-header d-md-none" :class="{ 'drawer-open': drawerOpen }">
            <button
                class="drawer-toggle"
                @click="toggleDrawer"
                :class="{ 'active': drawerOpen }"
            >
                <span class="hamburger-line"></span>
                <span class="hamburger-line"></span>
                <span class="hamburger-line"></span>
            </button>

            <div class="current-page-title">
                <v-icon class="page-icon">{{ currentPageIcon }}</v-icon>
                <span class="page-name">{{ currentPageName.toUpperCase() }}</span>
            </div>
        </div>

        <!-- Desktop: Navigation Tabs -->
        <div class="desktop-nav d-none d-md-flex">
            <!-- Home Tab (Left Corner) -->
            <button
                @click="openPage('inicio')"
                class="nav-tab home-tab"
                :class="{ 'active': currentTab === 0 }"
            >
                <div class="tab-content">
                    <v-icon class="tab-icon">mdi-home</v-icon>
                    <span class="tab-ripple"></span>
                </div>
            </button>

            <!-- Center Group -->
            <div class="nav-tabs-center">
                <!-- Agenda -->
                <button
                    @click="openPage('agenda')"
                    class="nav-tab"
                    :class="{ 'active': currentTab === 1 }"
                >
                    <div class="tab-content">
                        <v-icon class="tab-icon">mdi-calendar-month</v-icon>
                        <span class="tab-label">{{ $t('mainNav.agenda').toUpperCase() }}</span>
                        <span class="tab-ripple"></span>
                    </div>
                </button>

                <!-- Pacientes -->
                <button
                    @click="openPage('pacientes')"
                    class="nav-tab"
                    :class="{ 'active': currentTab === 2 }"
                >
                    <div class="tab-content">
                        <v-icon class="tab-icon">mdi-account-details</v-icon>
                        <span class="tab-label">{{ $t('mainNav.patients').toUpperCase() }}</span>
                        <span class="tab-ripple"></span>
                    </div>
                </button>

                <!-- Mentorias -->
                <button
                    @click="openPage('mentorias')"
                    class="nav-tab"
                    :class="{ 'active': currentTab === 3 }"
                >
                    <div class="tab-content">
                        <v-icon class="tab-icon">mdi-school</v-icon>
                        <span class="tab-label">{{ $t('mainNav.mentoring').toUpperCase() }}</span>
                        <span class="tab-ripple"></span>
                    </div>
                </button>

                <!-- Financeiro -->
                <button
                    @click="openPage('financeiro')"
                    class="nav-tab"
                    :class="{ 'active': currentTab === 4 }"
                >
                    <div class="tab-content">
                        <v-icon class="tab-icon">mdi-currency-usd</v-icon>
                        <span class="tab-label">{{ $t('mainNav.financial').toUpperCase() }}</span>
                        <span class="tab-ripple"></span>
                    </div>
                </button>

                <!-- Clínicas (Admin only) -->
                <button
                    v-if="$user?.system_admin"
                    @click="openPage('clinicas')"
                    class="nav-tab admin-tab"
                    :class="{ 'active': currentTab === 5 }"
                >
                    <div class="tab-content">
                        <v-icon class="tab-icon">mdi-hospital-building</v-icon>
                        <span class="tab-ripple"></span>
                    </div>
                </button>

                <!-- Active indicator -->
                <div class="active-indicator" :style="indicatorStyle"></div>
            </div>
        </div>

        <!-- Notifications -->
        <div class="notification-container">
            <notification-dropdown />
        </div>

    </nav>

    <!-- Mobile Drawer - Teleportado para o body -->
    <teleport to="body">
        <transition name="drawer">
            <div v-if="drawerOpen" class="mobile-drawer d-md-none">
                <!-- Spacer para o hamburger -->
                <div class="drawer-spacer"></div>

                <div class="drawer-content">
                    <button
                        v-for="(item, index) in navigationItems"
                        :key="index"
                        @click="navigateFromDrawer(item.page)"
                        class="drawer-item"
                        :class="{ 'active': currentTab === index }"
                    >
                        <v-icon class="drawer-item-icon">{{ item.icon }}</v-icon>
                        <span class="drawer-item-label">{{ item.label.toUpperCase() }}</span>
                        <v-icon v-if="currentTab === index" class="drawer-item-check">mdi-check</v-icon>
                    </button>
                </div>
            </div>
        </transition>

        <!-- Drawer Overlay - Teleportado para o body para cobrir toda a tela -->
        <transition name="overlay">
            <div v-if="drawerOpen" class="drawer-overlay d-md-none" @click="closeDrawer"></div>
        </transition>
    </teleport>
</template>

<script>
import { useRoute } from 'vue-router';
import NotificationDropdown from '@/components/NotificationDropdown.vue';

export default {
    name: "tab-navigation",
    components: {
        NotificationDropdown
    },
    data() {
        return {
            currentTab: 0,
            drawerOpen: false,
            indicatorStyle: {}
        }
    },
    computed: {
        route: () => useRoute(),

        navigationItems() {
            const items = [
                { page: 'inicio', icon: 'mdi-home', label: 'Início' },
                { page: 'agenda', icon: 'mdi-calendar-month', label: this.$t('mainNav.agenda') },
                { page: 'pacientes', icon: 'mdi-account-details', label: this.$t('mainNav.patients') },
                { page: 'mentorias', icon: 'mdi-school', label: this.$t('mainNav.mentoring') },
                { page: 'financeiro', icon: 'mdi-currency-usd', label: this.$t('mainNav.financial') }
            ];

            if (this.$user?.system_admin) {
                items.push({ page: 'clinicas', icon: 'mdi-hospital-building', label: 'Clínicas' });
            }

            return items;
        },

        currentPageIcon() {
            return this.navigationItems[this.currentTab]?.icon || 'mdi-home';
        },

        currentPageName() {
            return this.navigationItems[this.currentTab]?.label || 'Início';
        }
    },
    methods: {
        openPage(page) {
            this.$router.push(`/${page}`);
            this.updateIndicator();
        },

        navigateFromDrawer(page) {
            this.openPage(page);
            this.closeDrawer();
        },

        toggleDrawer() {
            this.drawerOpen = !this.drawerOpen;
            if (this.drawerOpen) {
                document.body.style.overflow = 'hidden';
            } else {
                document.body.style.overflow = '';
            }
        },

        closeDrawer() {
            this.drawerOpen = false;
            document.body.style.overflow = '';
        },

        updateCurrentTab() {
            let currentPathObject = this.$router.currentRoute.value.path.replace('/', '').split('/')[0];

            const tabsMap = {
                '': 0,
                'inicio': 0,
                'agenda': 1,
                'paciente': 2,
                'pacientes': 2,
                'mentorias': 3,
                'financeiro': 4,
                'ortodontista': 5,
                'ortodontistas': 5,
                'clinica': 5,
                'clinicas': 5,
            };

            this.currentTab = tabsMap[currentPathObject] || 0;
            this.$nextTick(() => {
                this.updateIndicator();
            });
        },

        updateIndicator() {
            this.$nextTick(() => {
                const activeTab = document.querySelector('.nav-tabs-center .nav-tab.active');
                if (activeTab) {
                    const centerWrapper = document.querySelector('.nav-tabs-center');
                    if (centerWrapper) {
                        const wrapperRect = centerWrapper.getBoundingClientRect();
                        const tabRect = activeTab.getBoundingClientRect();

                        this.indicatorStyle = {
                            left: `${tabRect.left - wrapperRect.left}px`,
                            width: `${tabRect.width}px`
                        };
                    }
                }
            });
        }
    },
    mounted() {
        this.updateCurrentTab();
        window.addEventListener('resize', this.updateIndicator);
    },
    beforeUnmount() {
        window.removeEventListener('resize', this.updateIndicator);
        document.body.style.overflow = '';
    },
    watch: {
        '$route'() {
            this.updateCurrentTab();
            this.closeDrawer();
        }
    }
}
</script>

<style scoped>
/* ===== MODERN NAVBAR ===== */
.modern-navbar {
    position: relative;
    display: flex;
    align-items: center;
    min-height: 48px;
    padding: 0;
    background: linear-gradient(135deg, #2d4e6f 0%, #225070 50%, #2a5373 100%);
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15), 0 1px 3px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    z-index: 1000;
}

/* Add subtle animated gradient overlay */
.modern-navbar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        90deg,
        rgba(95, 140, 175, 0.08) 0%,
        rgba(41, 85, 119, 0.04) 50%,
        rgba(95, 140, 175, 0.08) 100%
    );
    animation: shimmer 8s ease-in-out infinite;
    pointer-events: none;
}

@keyframes shimmer {
    0%, 100% { opacity: 0; }
    50% { opacity: 1; }
}

/* ===== MOBILE HEADER ===== */
.mobile-header {
    display: flex;
    align-items: center;
    width: 100%;
    position: relative;
    z-index: 10002;
    height: 48px;
}

.mobile-header.drawer-open .current-page-title {
    opacity: 0;
    pointer-events: none;
}

/* Hamburger Menu - Harmonizado com o botão de notificações */
.drawer-toggle {
    position: relative;
    width: 70px;
    height: 48px;
    background: rgba(255, 255, 255, 0.05);
    border: none;
    border-radius: 0px 3px 3px 0px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 4px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    flex-shrink: 0;
    z-index: 10002;
}

.drawer-toggle:hover {
    background: rgba(255, 255, 255, 0.1);
}

.drawer-toggle:active {
    background: rgba(255, 255, 255, 0.15);
}

.hamburger-line {
    width: 22px;
    height: 2px;
    background: white;
    border-radius: 2px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.drawer-toggle.active .hamburger-line:nth-child(1) {
    transform: translateY(6px) rotate(45deg);
}

.drawer-toggle.active .hamburger-line:nth-child(2) {
    opacity: 0;
    transform: translateX(-10px);
}

.drawer-toggle.active .hamburger-line:nth-child(3) {
    transform: translateY(-6px) rotate(-45deg);
}

/* Current Page Title */
.current-page-title {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    flex: 1;
    color: white;
    font-weight: 500;
    font-size: 0.75rem;
    letter-spacing: 0.5px;
    text-align: center;
    transition: opacity 0.2s ease;
}

.page-icon {
    color: #7BA5C8;
    font-size: 1.2rem !important;
}

.page-name {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* ===== DESKTOP NAVIGATION ===== */
.desktop-nav {
    position: relative;
    z-index: 1;
    display: flex;
    align-items: stretch;
    flex: 1;
    height: 48px;
}

.nav-tabs-center {
    position: relative;
    display: flex;
    align-items: stretch;
    justify-content: center;
    gap: 0;
    flex: 1;
    height: 48px;
}

.nav-tab {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 0 1.5rem;
    background: transparent;
    border: none;
    border-radius: 0;
    color: rgba(255, 255, 255, 0.7);
    font-weight: 500;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.35s cubic-bezier(0.34, 1.56, 0.64, 1);
    overflow: visible;
    flex: 1;
    min-width: 0;
}

.nav-tab .tab-content {
    position: relative;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    z-index: 1;
    transition: all 0.35s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.nav-tab .tab-icon {
    font-size: 1.2rem !important;
    transition: all 0.35s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.nav-tab .tab-label {
    transition: all 0.35s cubic-bezier(0.34, 1.56, 0.64, 1);
    white-space: nowrap;
    font-size: 0.8rem;
    letter-spacing: 0.5px;
    font-weight: 500;
}

/* Click effect - subtle flash */
.tab-ripple {
    position: absolute;
    inset: 0;
    background: rgba(255, 255, 255, 0.15);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.nav-tab:active .tab-ripple {
    opacity: 1;
    transition: opacity 0s;
}

/* Hover state */
.nav-tab:hover {
    color: white;
    background: rgba(255, 255, 255, 0.08);
}

.nav-tab:hover .tab-icon {
    transform: scale(1.15) rotate(5deg);
}

.nav-tab:hover .tab-content {
    transform: scale(1.02);
}

/* Active state */
.nav-tab.active {
    color: white;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.08) 100%);
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15);
    position: relative;
}

.nav-tab.active::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, rgba(143, 184, 217, 0.15), transparent);
    opacity: 0;
    animation: glowPulse 2s ease-in-out infinite;
    pointer-events: none;
}

@keyframes glowPulse {
    0%, 100% {
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
}

.nav-tab.active .tab-icon {
    color: #B5D1E8;
    transform: scale(1.15);
    filter: drop-shadow(0 2px 6px rgba(181, 209, 232, 0.4));
}

.nav-tab.active .tab-content {
    transform: scale(1.03);
}

.nav-tab.active .tab-label {
    font-weight: 600;
    letter-spacing: 0.3px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Home tab - icon only (left corner) */
.home-tab {
    flex: 0 0 auto;
    width: 64px;
    padding: 0;
    margin-right: auto;
}

.home-tab .tab-label {
    display: none;
}

/* Admin tab - icon only (in center group) */
.admin-tab {
    flex: 0 0 auto;
    width: 64px;
    padding: 0;
}

.admin-tab .tab-label {
    display: none;
}

/* Active indicator */
.active-indicator {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 2px;
    background: linear-gradient(90deg, #7BA5C8, #ECECEC, #7BA5C8);
    transition: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
    box-shadow: 0 0 8px rgba(123, 165, 200, 0.6);
}


/* ===== NOTIFICATIONS ===== */
.notification-container {
    position: relative;
    z-index: 1;
    flex-shrink: 0;
}

.notification-container :deep(.notification-dropdown) {
    z-index: 1050;
}

/* ===== MOBILE DRAWER - Estilo "Abas de Fichário" Moderno ===== */
.mobile-drawer {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 260px;
    height: auto !important;
    max-height: calc(100vh - 80px);
    max-height: calc(100dvh - 80px);
    background: transparent;
    z-index: 10001 !important;
    display: flex;
    flex-direction: column;
    overflow: visible;
    pointer-events: none;
}

.drawer-spacer {
    width: 100%;
    height: 64px;
    flex-shrink: 0;
    background: transparent;
    pointer-events: none;
}

.drawer-content {
    padding: 0;
    display: flex;
    flex-direction: column;
    gap: 0;
    position: relative;
    z-index: 1;
    pointer-events: auto;
}

/* Estilo de "Aba de Fichário" Moderno */
.drawer-item {
    position: relative;
    display: flex;
    align-items: center;
    gap: 1rem;
    width: 100%;
    padding: 1.25rem 1.5rem 1.25rem 1.75rem;
    background: linear-gradient(135deg, #2a3f54 0%, #1f2d3d 100%);
    border: none;
    border-radius: 0 24px 24px 0;
    color: rgba(255, 255, 255, 0.75);
    font-size: 0.85rem;
    font-weight: 500;
    letter-spacing: 0.5px;
    text-align: left;
    cursor: pointer;
    transition: all 0.35s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
        3px 3px 10px rgba(0, 0, 0, 0.25),
        inset 0 1px 0 rgba(255, 255, 255, 0.05);
    transform-origin: left center;
    margin-bottom: 2px;
}

/* Efeito de borda da aba */
.drawer-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(180deg, #5B84A4 0%, #7BA5C8 100%);
    border-radius: 0 2px 2px 0;
    opacity: 0;
    transition: opacity 0.3s ease;
}

/* Bolinha indicadora */
.drawer-item::after {
    content: '';
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: transparent;
    border: 2px solid transparent;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.drawer-item:hover {
    background: linear-gradient(135deg, #324b62 0%, #253544 100%);
    color: white;
    transform: translateX(6px);
    box-shadow:
        4px 4px 14px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.08);
}

.drawer-item:hover::before {
    opacity: 0.5;
}

.drawer-item:hover::after {
    border-color: rgba(123, 165, 200, 0.5);
    width: 11px;
    height: 11px;
}

.drawer-item.active {
    background: linear-gradient(135deg, #3a5570 0%, #2d4254 100%);
    color: white;
    transform: translateX(10px);
    box-shadow:
        5px 5px 18px rgba(0, 0, 0, 0.35),
        inset 0 1px 0 rgba(255, 255, 255, 0.1),
        inset 0 0 20px rgba(123, 165, 200, 0.1);
}

.drawer-item.active::before {
    opacity: 1;
}

/* Bolinha preenchida para item ativo */
.drawer-item.active::after {
    background: #8FB8D9;
    border-color: #8FB8D9;
    box-shadow: 0 0 10px rgba(143, 184, 217, 0.7);
    width: 12px;
    height: 12px;
}

.drawer-item-icon {
    font-size: 1.4rem !important;
    color: #5B84A4;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.drawer-item:hover .drawer-item-icon {
    transform: scale(1.1) rotate(5deg);
    color: #7BA5C8;
}

.drawer-item.active .drawer-item-icon {
    transform: scale(1.15);
    color: #8FB8D9;
    filter: drop-shadow(0 2px 4px rgba(143, 184, 217, 0.3));
}

.drawer-item-label {
    flex: 1;
    transition: transform 0.3s ease;
}

.drawer-item:hover .drawer-item-label {
    transform: translateX(2px);
}

/* Remover o ícone de check - agora usamos a bolinha */
.drawer-item-check {
    display: none;
}

/* Drawer Overlay - Garantir que cubra toda a tela */
.drawer-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    width: 100vw !important;
    width: 100dvw !important;
    height: 100vh !important;
    height: 100dvh !important;
    min-height: 100vh !important;
    min-height: 100dvh !important;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(2px);
    z-index: 10000 !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* ===== ANIMATIONS ===== */
/* Tab transition animation */
@keyframes tabActivate {
    0% {
        transform: scale(0.95);
        opacity: 0.7;
    }
    50% {
        transform: scale(1.02);
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.nav-tab.active {
    animation: tabActivate 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
}

/* Icon pulse on active */
@keyframes iconPulse {
    0%, 100% {
        transform: scale(1.15);
    }
    50% {
        transform: scale(1.25);
    }
}

.nav-tab.active .tab-icon {
    animation: iconPulse 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
}

/* Drawer slide animation */
.drawer-enter-active,
.drawer-leave-active {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.drawer-enter-from,
.drawer-leave-to {
    transform: translateX(-100%);
}

/* Overlay fade animation */
.overlay-enter-active,
.overlay-leave-active {
    transition: opacity 0.3s ease;
}

.overlay-enter-from,
.overlay-leave-to {
    opacity: 0;
}

/* ===== RESPONSIVE ===== */
@media (max-width: 767.98px) {
    .modern-navbar {
        min-height: 48px;
        padding: 0;
    }

    .current-page-title {
        font-size: 0.75rem;
        letter-spacing: 0.5px;
    }

    .page-icon {
        font-size: 1.2rem !important;
    }

    /* Ajustar drawer para mobile */
    .mobile-drawer {
        width: 240px !important;
    }

    .drawer-item {
        padding: 1.15rem 1.35rem 1.15rem 1.5rem;
        font-size: 0.8rem;
    }

    .drawer-item-icon {
        font-size: 1.3rem !important;
    }
}

@media (max-width: 480px) {
    .drawer-toggle {
        width: 60px;
    }

    .mobile-drawer {
        width: 220px !important;
    }

    .drawer-item {
        padding: 1rem 1.15rem 1rem 1.25rem;
        font-size: 0.75rem;
    }

    .drawer-item-icon {
        font-size: 1.2rem !important;
    }
}

@media (min-width: 768px) {
    /* Padding removido para alinhar o botão home à esquerda */
}

@media (min-width: 768px) and (max-width: 991.98px) {
    .nav-tab {
        padding: 0 1rem;
    }

    .nav-tab .tab-icon {
        font-size: 1.1rem !important;
    }

    .nav-tab .tab-label {
        font-size: 0.75rem;
    }

    .home-tab,
    .admin-tab {
        width: 56px;
    }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
    .nav-tab {
        padding: 0 1.25rem;
    }
}

@media (min-width: 1200px) {
    .nav-tab {
        padding: 0 1.75rem;
    }
}

/* ===== ACCESSIBILITY ===== */
.nav-tab:focus-visible,
.drawer-toggle:focus-visible,
.drawer-close:focus-visible,
.drawer-item:focus-visible {
    outline: 2px solid #5B84A4;
    outline-offset: 2px;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
</style>
