<template>
  <div class="paciente-financeiro">
    <!-- Container Principal com Duas Colunas -->
    <div class="card rounded-0 mb-3">
      <div class="card-body p-0">
        <div class="row g-0 h-100">

          <!-- ========== COLUNA ESQUERDA - ORÇAMENTOS / RESUMO FATURA ========== -->
          <div class="col-lg-4 col-md-5 border-end">
            <div class="orcamentos-section h-100">
              <!-- Header dos Orçamentos -->
              <div class="section-header section-header-orcamento" :class="viewMode === 'creating-orcamento' ? 'bg-gradient-green-subtle' : 'bg-gradient-blue-subtle'" :style="viewMode === 'creating-orcamento' ? 'background: linear-gradient(135deg, rgba(40, 167, 69, 0.08) 0%, rgba(25, 135, 84, 0.12) 100%) !important;' : ''">
                <div class="d-flex justify-content-between align-items-center w-100">
                  <div class="d-flex align-items-center">
                    <i class="fas fa-calculator me-2" :class="viewMode === 'creating-orcamento' ? 'text-success' : 'text-primary'"></i>
                    <h6 class="mb-0 font-weight-bold text-uppercase" style="font-size: 0.8rem; letter-spacing: 0.5px;">
                      {{ viewMode === 'creating-orcamento' ? (editingOrcamento ? 'Editar Orçamento' : 'Novo Orçamento') : viewMode === 'creating-fatura' ? 'Resumo da Fatura' : viewMode === 'viewing-orcamento' ? 'Detalhes do Orçamento' : 'Orçamentos' }}
                    </h6>
                    <span v-if="viewMode === 'normal' && orcamentos.length > 0" class="badge bg-primary ms-2">{{ orcamentos.length }}</span>
                  </div>
                  <button v-if="viewMode === 'normal'" class="btn btn-sm btn-primary" @click="startCreatingOrcamento">
                    <i class="bi bi-plus-lg me-1"></i>
                    Novo
                  </button>
                  <button v-else-if="viewMode === 'viewing-orcamento'" class="btn btn-sm btn-outline-secondary" @click="voltarParaLista">
                    <i class="fas fa-arrow-left me-1"></i>
                    Voltar
                  </button>
                  <button v-else class="btn-cancel-edit-orcamento" @click="cancelCreation">
                    <i class="bi bi-x-lg"></i>
                    CANCELAR
                  </button>
                </div>
              </div>

              <!-- Conteúdo dos Orçamentos (Modo Normal) -->
              <div v-if="viewMode === 'normal'" class="section-content">
                <div v-if="loading.orcamentos" class="text-center py-4">
                  <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Carregando orçamentos...</span>
                  </div>
                </div>
                <div v-else-if="orcamentos.length === 0" class="text-center py-4 text-muted">
                  <i class="fas fa-calculator fa-2x mb-2 opacity-50"></i>
                  <p class="mb-0 small">Nenhum orçamento encontrado</p>
                  <small class="text-muted">Clique em "Novo" para criar</small>
                </div>
                <div v-else class="orcamentos-list">
                  <div
                    v-for="orcamento in orcamentos"
                    :key="orcamento.id"
                    class="orcamento-item"
                    @click="viewOrcamento(orcamento)"
                  >
                    <div class="orcamento-item-header">
                      <div class="orcamento-item-title">
                        <h6 class="mb-0 small">{{ orcamento.titulo }}</h6>
                        <small class="text-muted">#{{ orcamento.numero || orcamento.id }}</small>
                      </div>
                      <span class="badge badge-sm" :class="getOrcamentoStatusBadgeClass(orcamento.status)">
                        {{ getOrcamentoStatusText(orcamento.status) }}
                      </span>
                    </div>

                    <div class="orcamento-item-body">
                      <div class="orcamento-item-info">
                        <div class="info-row">
                          <i class="fas fa-calendar-alt text-muted me-1"></i>
                          <span class="text-sm">{{ formatDate(orcamento.data_orcamento) }}</span>
                        </div>
                        <div class="info-row" v-if="orcamento.data_validade">
                          <i class="fas fa-clock text-muted me-1"></i>
                          <span class="text-sm">Válido até {{ formatDate(orcamento.data_validade) }}</span>
                        </div>
                      </div>

                      <div class="orcamento-item-valor">
                        <div class="valor-principal">{{ formatCurrency(orcamento.valor_final) }}</div>
                        <small v-if="orcamento.valor_total !== orcamento.valor_final" class="valor-original text-muted">
                          De {{ formatCurrency(orcamento.valor_total) }}
                        </small>
                      </div>
                    </div>

                    <div class="orcamento-item-actions">
                      <button
                        class="btn btn-sm btn-outline-info"
                        @click.stop="generateOrcamentoPDF(orcamento)"
                        title="Gerar PDF"
                      >
                        <i class="fas fa-file-pdf"></i>
                      </button>
                      <div class="custom-dropdown" @click.stop>
                        <button
                          class="btn btn-sm btn-outline-secondary dropdown-toggle-custom"
                          @click="toggleDropdown(orcamento.id)"
                          title="Mais opções"
                        >
                          <i class="fas fa-ellipsis-v"></i>
                        </button>
                        <div
                          v-if="activeDropdown === orcamento.id"
                          class="custom-dropdown-menu"
                          @click.stop
                        >
                          <!-- Opções de Ação Principal -->
                          <div v-if="orcamento.status === 'rascunho' || orcamento.status === 'enviado'" class="dropdown-item" @click="generateFaturaFromOrcamento(orcamento)">
                            <i class="fas fa-file-invoice me-2 text-success"></i>
                            Gerar Fatura
                          </div>
                          <div v-if="canConvertOrcamento(orcamento)" class="dropdown-item" @click="generateFaturaFromOrcamento(orcamento)">
                            <i class="fas fa-arrow-right me-2 text-success"></i>
                            Converter em Fatura
                          </div>

                          <!-- Opções de Status -->
                          <div v-if="orcamento.status === 'rascunho'" class="dropdown-item" @click="enviarOrcamento(orcamento)">
                            <i class="fas fa-paper-plane me-2 text-info"></i>
                            Enviar para Aprovação
                          </div>
                          <div v-if="canApproveOrcamento(orcamento)" class="dropdown-item" @click="aprovarOrcamento(orcamento)">
                            <i class="fas fa-check-circle me-2 text-success"></i>
                            Aprovar Orçamento
                          </div>
                          <div v-if="orcamento.status === 'enviado'" class="dropdown-item" @click="rejeitarOrcamento(orcamento)">
                            <i class="fas fa-ban me-2 text-warning"></i>
                            Recusar Orçamento
                          </div>

                          <!-- Separador -->
                          <div v-if="orcamento.status === 'rascunho' || canApproveOrcamento(orcamento) || orcamento.status === 'enviado' || canConvertOrcamento(orcamento)" class="dropdown-divider"></div>

                          <!-- Opções de Edição -->
                          <div v-if="canEditOrcamento(orcamento)" class="dropdown-item" @click="editOrcamento(orcamento)">
                            <i class="fas fa-edit me-2 text-primary"></i>
                            Editar Orçamento
                          </div>
                          <div class="dropdown-item" @click="duplicarOrcamento(orcamento)">
                            <i class="fas fa-copy me-2 text-info"></i>
                            Duplicar Orçamento
                          </div>

                          <!-- Opções de Exportação -->
                          <div class="dropdown-divider"></div>
                          <div class="dropdown-item" @click="generateOrcamentoPDF(orcamento)">
                            <i class="fas fa-file-pdf me-2 text-danger"></i>
                            Exportar PDF
                          </div>
                          <div class="dropdown-item" @click="enviarPorEmail(orcamento)">
                            <i class="fas fa-envelope me-2 text-info"></i>
                            Enviar por E-mail
                          </div>

                          <!-- Opções Perigosas -->
                          <div class="dropdown-divider"></div>
                          <div class="dropdown-item text-danger" @click="excluirOrcamento(orcamento)">
                            <i class="fas fa-trash me-2"></i>
                            Excluir Orçamento
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Conteúdo: Informações do Orçamento (Modo Criando Orçamento) -->
              <div v-else-if="viewMode === 'creating-orcamento'" class="section-content p-3">
                <div class="orcamento-resumo-card">
                  <!-- Campos do Resumo -->
                  <div class="row g-3">
                    <div class="col-12">
                      <label class="form-label elegant-label-orcamento">Título *</label>
                      <input type="text"
                             ref="tituloOrcamentoInput"
                             class="form-control elegant-input-orcamento"
                             v-model="novoOrcamentoForm.titulo"
                             placeholder="Ex: Tratamento Ortodôntico">
                    </div>

                    <div class="col-12">
                      <label class="form-label elegant-label-orcamento">Ortodontista</label>
                      <select class="form-select elegant-input-orcamento elegant-select-orcamento" v-model="novoOrcamentoForm.dentista_id">
                        <option value="">Selecione um ortodontista</option>
                        <option v-for="dentista in dentistas" :key="dentista?.id || Math.random()" :value="dentista?.id">
                          {{ dentista?.nome || 'Ortodontista sem nome' }}
                        </option>
                      </select>
                    </div>

                    <div class="col-12">
                      <label class="form-label elegant-label-orcamento">Descrição</label>
                      <textarea class="form-control elegant-input-orcamento"
                                rows="3"
                                v-model="novoOrcamentoForm.descricao"
                                placeholder="Descrição do tratamento"></textarea>
                    </div>
                  </div>

                  <!-- Resumo de Valores (Reutilizável) - Oculto até iniciar orçamento -->
                  <component :is="'div'" v-if="orcamentoIniciado" class="valores-resumo p-3 bg-light rounded mb-3 mt-4">
                    <div class="d-flex justify-content-between mb-2">
                      <span class="text-uppercase fw-bold text-muted" style="font-size: 0.7rem; letter-spacing: 0.5px;">Valor Total:</span>
                      <span class="fw-bold fs-6 text-dark">{{ formatCurrency(getValorTotalResumo()) }}</span>
                    </div>
                    <div v-if="getValorTotalResumo() !== getValorAprovadoResumo()" class="d-flex justify-content-between mb-2">
                      <span class="text-uppercase fw-bold text-muted" style="font-size: 0.7rem; letter-spacing: 0.5px;">Desconto/Rejeições:</span>
                      <span class="text-warning fw-bold fs-6">-{{ formatCurrency(getValorTotalResumo() - getValorAprovadoResumo()) }}</span>
                    </div>
                    <hr>
                    <div class="d-flex justify-content-between">
                      <span class="text-uppercase fw-bold text-dark" style="font-size: 0.75rem; letter-spacing: 0.5px;">Valor Final (aprovado):</span>
                      <span class="fw-bold text-success fs-4">{{ formatCurrency(getValorAprovadoResumo()) }}</span>
                    </div>
                  </component>

                  <!-- Botão Iniciar Orçamento - Oculto após iniciar -->
                  <button v-if="!orcamentoIniciado" class="btn btn-primary w-100" @click="iniciarOrcamento">
                    <i class="fas fa-play me-2"></i>Iniciar Orçamento
                  </button>

                  <!-- Botão Gerar Fatura - Visível após iniciar, desabilitado até ter itens aprovados -->
                  <button v-else
                          class="btn btn-success w-100"
                          @click="gerarFaturaDoFormulario"
                          :disabled="!hasItensAprovados">
                    <i class="fas fa-file-invoice-dollar me-2"></i>Gerar Fatura
                  </button>
                </div>
              </div>

              <!-- Conteúdo: Informações Básicas da Fatura (Modo Criando Fatura) - Coluna Esquerda -->
              <div v-else-if="viewMode === 'creating-fatura'" class="section-content p-3">
                <div class="fatura-info-basica">
                  <h6 class="text-primary mb-3">
                    <i class="fas fa-info-circle me-2"></i>Informações Básicas
                  </h6>

                  <div class="row g-3">
                    <!-- Descrição -->
                    <div class="col-12">
                      <label class="form-label small fw-bold">Descrição *</label>
                      <input type="text"
                             class="form-control"
                             v-model="novaFaturaForm.descricao"
                             :class="{ 'is-invalid': faturaErrors.descricao }"
                             placeholder="Ex: Mensalidade, Consulta, Procedimento...">
                      <div v-if="faturaErrors.descricao" class="invalid-feedback">
                        {{ faturaErrors.descricao }}
                      </div>
                    </div>

                    <!-- Ortodontista -->
                    <div class="col-12">
                      <label class="form-label small fw-bold">Ortodontista</label>
                      <select class="form-select" v-model="novaFaturaForm.dentista_id">
                        <option value="">Selecione um ortodontista</option>
                        <option v-for="dentista in dentistas" :key="dentista?.id || Math.random()" :value="dentista?.id">
                          {{ dentista?.nome || 'Ortodontista sem nome' }}
                        </option>
                      </select>
                    </div>

                    <!-- Observações -->
                    <div class="col-12">
                      <label class="form-label small fw-bold">Observações</label>
                      <textarea class="form-control"
                                rows="4"
                                v-model="novaFaturaForm.observacoes"
                                placeholder="Observações adicionais sobre a fatura..."></textarea>
                    </div>
                  </div>

                  <!-- Resumo Visual -->
                  <div class="valores-resumo mt-4 p-3 bg-light rounded">
                    <div class="d-flex justify-content-between mb-2">
                      <span class="small text-muted">Valor Nominal:</span>
                      <span class="fw-bold">{{ formatCurrency(novaFaturaForm.valor_nominal || 0) }}</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                      <span class="small text-muted">Parcelas:</span>
                      <span class="fw-bold">{{ novaFaturaForm.parcelas_total || 1 }}x</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                      <span class="small text-muted">Valor/Parcela:</span>
                      <span class="fw-bold text-info">{{ formatCurrency(valorPorParcelaFatura) }}</span>
                    </div>
                    <hr>
                    <div class="d-flex justify-content-between">
                      <span class="fw-bold">Valor Final:</span>
                      <span class="fw-bold text-success fs-5">{{ formatCurrency(valorFinalFatura) }}</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Conteúdo: Detalhes do Orçamento (Modo Visualizando Orçamento) -->
              <div v-else-if="viewMode === 'viewing-orcamento' && orcamentoSelecionado" class="section-content p-3">
                <div class="orcamento-detalhes-card">
                  <!-- Informações Básicas -->
                  <div class="orcamento-info mb-4">
                    <div class="mb-3">
                      <!-- Título editável -->
                      <div v-if="!editingOrcamentoTitle" class="d-flex justify-content-between align-items-start">
                        <div class="d-flex align-items-center">
                          <h5 class="text-dark fw-bold mb-1 me-2">{{ orcamentoSelecionado?.titulo || 'Orçamento' }}</h5>
                          <button v-if="canEditOrcamento(orcamentoSelecionado)"
                                  class="btn btn-sm btn-link p-0 text-muted"
                                  @click="startEditingTitle"
                                  title="Editar título">
                            <i class="fas fa-edit" style="font-size: 0.8rem;"></i>
                          </button>
                        </div>
                        <span class="badge" :class="getOrcamentoStatusBadgeClass(orcamentoSelecionado?.status)">
                          {{ getOrcamentoStatusText(orcamentoSelecionado?.status) }}
                        </span>
                      </div>
                      <div v-else class="d-flex align-items-center gap-2 mb-2">
                        <input v-model="editingTitleValue"
                               class="form-control"
                               @keyup.enter="saveTitle"
                               @keyup.escape="cancelEditTitle"
                               ref="titleInput"
                               placeholder="Digite o título do orçamento">
                        <button class="btn btn-success px-3 py-2" @click="saveTitle" style="min-width: 45px;">
                          <i class="fas fa-check"></i>
                        </button>
                        <button class="btn btn-secondary px-3 py-2" @click="cancelEditTitle" style="min-width: 45px;">
                          <i class="fas fa-times"></i>
                        </button>
                      </div>
                      <small class="text-muted">#{{ orcamentoSelecionado?.numero || orcamentoSelecionado?.id || 'N/A' }}</small>
                    </div>

                    <div class="row g-2 mb-3">
                      <div class="col-6">
                        <div class="d-flex align-items-center mb-1">
                          <small class="text-uppercase fw-bold text-muted me-2" style="font-size: 0.7rem; letter-spacing: 0.5px;">Data do Orçamento</small>
                          <button v-if="canEditOrcamento(orcamentoSelecionado) && !editingOrcamentoDate"
                                  class="btn btn-sm btn-link p-0 text-muted"
                                  @click="startEditingDate"
                                  title="Editar data">
                            <i class="fas fa-edit" style="font-size: 0.7rem;"></i>
                          </button>
                        </div>
                        <div v-if="!editingOrcamentoDate">
                          <span class="fw-bold fs-6 text-dark">{{ formatDate(orcamentoSelecionado?.data_orcamento) }}</span>
                        </div>
                        <div v-else class="d-flex align-items-center gap-1">
                          <input v-model="editingDateValue"
                                 type="date"
                                 class="form-control form-control-sm"
                                 @keyup.escape="cancelEditDate"
                                 ref="dateInput">
                          <button class="btn btn-success px-3 py-2" @click="saveDate" style="min-width: 45px;">
                            <i class="fas fa-check"></i>
                          </button>
                          <button class="btn btn-secondary px-3 py-2" @click="cancelEditDate" style="min-width: 45px;">
                            <i class="fas fa-times"></i>
                          </button>
                        </div>
                      </div>
                      <div class="col-6" v-if="orcamentoSelecionado?.data_validade">
                        <small class="text-uppercase fw-bold text-muted d-block" style="font-size: 0.7rem; letter-spacing: 0.5px;">Válido até</small>
                        <span class="fw-bold fs-6 text-dark">{{ formatDate(orcamentoSelecionado?.data_validade) }}</span>
                      </div>
                    </div>

                    <!-- Descrição editável -->
                    <div class="mb-3">
                      <div class="d-flex align-items-center mb-1">
                        <small class="text-uppercase fw-bold text-muted me-2" style="font-size: 0.7rem; letter-spacing: 0.5px;">Descrição</small>
                        <button v-if="canEditOrcamento(orcamentoSelecionado) && !editingOrcamentoDescription"
                                class="btn btn-sm btn-link p-0 text-muted"
                                @click="startEditingDescription"
                                title="Editar descrição">
                          <i class="fas fa-edit" style="font-size: 0.7rem;"></i>
                        </button>
                      </div>
                      <div v-if="!editingOrcamentoDescription">
                        <p class="mb-0 text-dark" style="line-height: 1.6;" v-if="orcamentoSelecionado?.descricao">{{ orcamentoSelecionado?.descricao }}</p>
                        <p class="mb-0 text-muted fst-italic" v-else>Clique no ícone para adicionar uma descrição</p>
                      </div>
                      <div v-else class="d-flex align-items-start gap-2">
                        <textarea v-model="editingDescriptionValue"
                                  class="form-control form-control-sm"
                                  rows="3"
                                  @keyup.escape="cancelEditDescription"
                                  ref="descriptionInput"></textarea>
                        <div class="d-flex flex-column gap-1">
                          <button class="btn btn-success px-3 py-2" @click="saveDescription" style="min-width: 45px;">
                            <i class="fas fa-check"></i>
                          </button>
                          <button class="btn btn-secondary px-3 py-2" @click="cancelEditDescription" style="min-width: 45px;">
                            <i class="fas fa-times"></i>
                          </button>
                        </div>
                      </div>
                    </div>

                    <!-- Resumo de Valores (Reutilizável) -->
                    <component :is="'div'" class="valores-resumo p-3 bg-light rounded mb-3">
                      <div class="d-flex justify-content-between mb-2">
                        <span class="text-uppercase fw-bold text-muted" style="font-size: 0.7rem; letter-spacing: 0.5px;">Valor Total:</span>
                        <span class="fw-bold fs-6 text-dark">{{ formatCurrency(getValorTotalResumo()) }}</span>
                      </div>
                      <div v-if="getValorTotalResumo() !== getValorAprovadoResumo()" class="d-flex justify-content-between mb-2">
                        <span class="text-uppercase fw-bold text-muted" style="font-size: 0.7rem; letter-spacing: 0.5px;">Desconto/Rejeições:</span>
                        <span class="text-warning fw-bold fs-6">-{{ formatCurrency(getValorTotalResumo() - getValorAprovadoResumo()) }}</span>
                      </div>
                      <hr>
                      <div class="d-flex justify-content-between">
                        <span class="text-uppercase fw-bold text-dark" style="font-size: 0.75rem; letter-spacing: 0.5px;">Valor Final (aprovado):</span>
                        <span class="fw-bold text-success fs-4">{{ formatCurrency(getValorAprovadoResumo()) }}</span>
                      </div>
                    </component>

                    <!-- Botões de Ação -->
                    <div class="d-flex gap-2">
                      <button
                        class="btn btn-sm btn-outline-info"
                        @click="generateOrcamentoPDF(orcamentoSelecionado)"
                        :disabled="generatingPDF"
                      >
                        <i class="fas fa-file-pdf me-1"></i>
                        {{ generatingPDF ? 'Gerando...' : 'PDF' }}
                      </button>
                      <!-- Botão "Gerar Fatura" para orçamento -->
                      <button
                        class="btn btn-sm btn-success"
                        @click="generateFaturaFromOrcamento(orcamentoSelecionado)"
                        :disabled="!hasItensAprovados"
                      >
                        <i class="fas fa-file-invoice me-1"></i>
                        Gerar Fatura
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- ========== COLUNA DIREITA - FATURAS / ITENS ORÇAMENTO ========== -->
          <div class="col-lg-8 col-md-7">
            <div class="faturas-section h-100">
              <!-- Header das Faturas -->
              <div class="section-header bg-light">
                <div class="d-flex justify-content-between align-items-center">
                  <div class="d-flex align-items-center">
                    <i class="fas fa-file-invoice-dollar me-2 text-primary"></i>
                    <h6 class="mb-0 font-weight-bold text-dark text-uppercase" style="font-size: 0.8rem; letter-spacing: 0.5px;">
                      {{ viewMode === 'creating-orcamento' ? 'Itens do Orçamento' : viewMode === 'viewing-orcamento' ? 'Itens do Orçamento' : 'Faturas' }}
                    </h6>
                    <span v-if="viewMode === 'normal' && faturas.length > 0" class="badge bg-primary ms-2">{{ faturas.length }}</span>
                  </div>
                  <button v-if="viewMode === 'normal'" class="btn btn-sm btn-primary" @click="startCreatingFatura">
                    <i class="bi bi-plus-lg me-1"></i>
                    Nova Fatura
                  </button>
                </div>
              </div>

              <!-- Filtros das Faturas (Modo Normal) -->
              <div v-if="viewMode === 'normal'" class="filters-section">
                <div class="row g-2">
                  <div class="col-12">
                    <div class="search-input-wrapper">
                      <i class="fas fa-search search-input-icon"></i>
                      <input type="text" class="form-control form-control-sm search-input-with-icon" v-model="filters.descricao" placeholder="Buscar por descrição...">
                    </div>
                  </div>
                </div>
              </div>


              <!-- Filtros por Status (Cards Clicáveis) (Modo Normal) -->
              <div v-if="viewMode === 'normal'" class="stats-section">
                <div class="row g-2">
                  <div class="col-6 col-lg-3">
                    <div
                      class="stats-card-toggle stats-card-pago"
                      :class="{ 'active': isStatusActive('pago'), 'disabled': !isStatusActive('pago') }"
                      @click="toggleStatus('pago')"
                    >
                      <div class="stats-icon" :class="isStatusActive('pago') ? 'text-success' : 'text-muted'">
                        <i class="fas fa-check-circle"></i>
                      </div>
                      <div class="stats-content">
                        <div class="stats-title">Pago</div>
                        <div class="stats-value" :class="isStatusActive('pago') ? 'text-success' : 'text-muted'">{{ formatCurrency(totalPago) }}</div>
                      </div>
                      <div class="toggle-checkbox">
                        <i class="fas fa-check" v-if="isStatusActive('pago')"></i>
                      </div>
                    </div>
                  </div>
                  <div class="col-6 col-lg-3">
                    <div
                      class="stats-card-toggle stats-card-pendente"
                      :class="{ 'active': isStatusActive('pendente'), 'disabled': !isStatusActive('pendente') }"
                      @click="toggleStatus('pendente')"
                    >
                      <div class="stats-icon" :class="isStatusActive('pendente') ? 'text-warning' : 'text-muted'">
                        <i class="fas fa-clock"></i>
                      </div>
                      <div class="stats-content">
                        <div class="stats-title">Pendente</div>
                        <div class="stats-value" :class="isStatusActive('pendente') ? 'text-warning' : 'text-muted'">{{ formatCurrency(totalPendente) }}</div>
                      </div>
                      <div class="toggle-checkbox">
                        <i class="fas fa-check" v-if="isStatusActive('pendente')"></i>
                      </div>
                    </div>
                  </div>
                  <div class="col-6 col-lg-3">
                    <div
                      class="stats-card-toggle stats-card-vencido"
                      :class="{ 'active': isStatusActive('vencido'), 'disabled': !isStatusActive('vencido') }"
                      @click="toggleStatus('vencido')"
                    >
                      <div class="stats-icon" :class="isStatusActive('vencido') ? 'text-danger' : 'text-muted'">
                        <i class="fas fa-exclamation-triangle"></i>
                      </div>
                      <div class="stats-content">
                        <div class="stats-title">Vencido</div>
                        <div class="stats-value" :class="isStatusActive('vencido') ? 'text-danger' : 'text-muted'">{{ formatCurrency(totalVencido) }}</div>
                      </div>
                      <div class="toggle-checkbox">
                        <i class="fas fa-check" v-if="isStatusActive('vencido')"></i>
                      </div>
                    </div>
                  </div>
                  <div class="col-6 col-lg-3">
                    <div
                      class="stats-card-toggle stats-card-cancelado"
                      :class="{ 'active': isStatusActive('cancelado'), 'disabled': !isStatusActive('cancelado') }"
                      @click="toggleStatus('cancelado')"
                    >
                      <div class="stats-icon" :class="isStatusActive('cancelado') ? 'text-secondary' : 'text-muted'">
                        <i class="fas fa-times-circle"></i>
                      </div>
                      <div class="stats-content">
                        <div class="stats-title">Cancelado</div>
                        <div class="stats-value" :class="isStatusActive('cancelado') ? 'text-secondary' : 'text-muted'">{{ formatCurrency(totalCancelado) }}</div>
                      </div>
                      <div class="toggle-checkbox">
                        <i class="fas fa-check" v-if="isStatusActive('cancelado')"></i>
                      </div>
                    </div>
                  </div>
                </div>
              </div>


              <!-- Lista de Faturas (Modo Normal) -->
              <div v-if="viewMode === 'normal'" class="faturas-table-section">
                <div class="table-responsive">
                  <table class="table align-items-center mb-0">
                <thead>
                  <tr>
                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">
                      Descrição
                    </th>
                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">
                      Valor
                    </th>
                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">
                      Emissão
                    </th>
                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">
                      Vencimento
                    </th>
                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">
                      Status
                    </th>
                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">
                      Ações
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-if="loading.faturas">
                    <td colspan="6" class="text-center py-4">
                      <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Carregando...</span>
                      </div>
                    </td>
                  </tr>
                  <tr v-else-if="filteredFaturas.length === 0">
                    <td colspan="6" class="text-center py-4 text-muted">
                      {{ faturas.length === 0 ? 'Nenhuma fatura encontrada para este paciente' : 'Nenhuma fatura corresponde aos filtros aplicados' }}
                    </td>
                  </tr>
                  <tr v-else v-for="fatura in filteredFaturas" :key="fatura.id">
                    <td>
                      <div class="d-flex px-2 py-1">
                        <div class="d-flex flex-column justify-content-center">
                          <h6 class="mb-0 text-sm">{{ fatura.descricao }}</h6>
                          <p class="text-xs text-secondary mb-0" v-if="fatura.observacoes">
                            {{ fatura.observacoes }}
                          </p>
                          <div v-if="fatura.parcelas_total > 1" class="text-xs text-info">
                            Parcela {{ fatura.parcela_numero }}/{{ fatura.parcelas_total }}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td class="align-middle text-center">
                      <span class="font-weight-bold">{{ formatCurrency(fatura.valor_final) }}</span>
                      <div v-if="fatura.valor_nominal !== fatura.valor_final" class="text-xs text-secondary">
                        Original: {{ formatCurrency(fatura.valor_nominal) }}
                      </div>
                    </td>
                    <td class="align-middle text-center">
                      <span class="text-secondary text-xs font-weight-bold">
                        {{ formatDate(fatura.data_emissao) }}
                      </span>
                    </td>
                    <td class="align-middle text-center">
                      <span class="text-secondary text-xs font-weight-bold">
                        {{ formatDate(fatura.data_vencimento) }}
                      </span>
                      <div v-if="isOverdue(fatura)" class="text-xs text-danger">
                        Vencida há {{ getDaysOverdue(fatura) }} dias
                      </div>
                    </td>
                    <td class="align-middle text-center">
                      <span class="badge badge-sm" :class="getStatusBadgeClass(fatura.status)">
                        {{ getStatusText(fatura.status) }}
                      </span>
                      <div v-if="fatura.data_pagamento" class="text-xs text-secondary mt-1">
                        Pago em {{ formatDate(fatura.data_pagamento) }}
                      </div>
                    </td>
                    <td class="align-middle text-center">
                      <div class="dropdown">
                        <button class="btn btn-link text-secondary mb-0" data-bs-toggle="dropdown">
                          <i class="fas fa-ellipsis-v"></i>
                        </button>
                        <ul class="dropdown-menu">
                          <li>
                            <a class="dropdown-item" href="#" @click.prevent="$emit('edit', fatura)">
                              <i class="fas fa-edit me-2"></i>
                              Editar
                            </a>
                          </li>
                          <li v-if="fatura.status === 'pendente'">
                            <a class="dropdown-item" href="#" @click.prevent="markAsPaid(fatura)">
                              <i class="fas fa-check me-2"></i>
                              Marcar como Pago
                            </a>
                          </li>
                          <li>
                            <a class="dropdown-item" href="#" @click.prevent="$emit('generate-receipt', fatura)">
                              <i class="fas fa-file-pdf me-2"></i>
                              Gerar Recibo
                            </a>
                          </li>
                          <li>
                            <hr class="dropdown-divider">
                          </li>
                          <li>
                            <a class="dropdown-item text-danger" href="#" @click.prevent="$emit('delete', fatura.id)">
                              <i class="fas fa-trash me-2"></i>
                              Cancelar
                            </a>
                          </li>
                        </ul>
                      </div>
                    </td>
                  </tr>
                </tbody>
                  </table>
                </div>
              </div>

              <!-- Conteúdo: Itens do Orçamento (Modo Criando Orçamento) - Reutiliza template de viewing -->
              <div v-else-if="viewMode === 'creating-orcamento'" class="orcamento-itens-view-section p-3">
                <!-- Grid de Itens (sempre visível) -->
                <div class="itens-grid">
                  <div class="row g-3">
                    <!-- Itens existentes -->
                    <div v-for="(item, index) in getItensParaExibir()" :key="index" class="col-12 col-md-6 col-lg-4">
                      <div class="item-card-grid p-3 bg-white border rounded-3 shadow-sm h-100 position-relative" :class="getItemStatusClass(item)">
                        <!-- Botões de edição/exclusão -->
                        <div class="item-actions position-absolute top-0 end-0 p-2">
                          <div class="d-flex gap-1">
                            <button class="btn btn-sm btn-outline-primary px-2 py-1" @click="editOrcamentoItem(index)" title="Editar">
                              <i class="fas fa-edit" style="font-size: 0.75rem;"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger px-2 py-1" @click="removeOrcamentoItem(index)" title="Remover">
                              <i class="fas fa-trash" style="font-size: 0.75rem;"></i>
                            </button>
                          </div>
                        </div>

                        <!-- Header com nome -->
                        <div class="d-flex justify-content-between align-items-start mb-2">
                          <h6 class="mb-0 text-primary fw-bold flex-grow-1" style="font-size: 1rem;">{{ item.nome || 'Item sem nome' }}</h6>
                        </div>

                        <!-- Descrição (se houver) -->
                        <div v-if="item.descricao" class="mb-3">
                          <p class="text-muted mb-0" style="font-size: 0.875rem; line-height: 1.5;">{{ item.descricao }}</p>
                        </div>

                        <!-- Dentes (se houver) -->
                        <div v-if="item.dentes && item.dentes.length > 0" class="mb-3">
                          <small class="text-uppercase fw-bold text-muted d-block mb-2" style="font-size: 0.7rem; letter-spacing: 0.5px;">Dentes:</small>
                          <div class="dentes-badges">
                            <span class="badge bg-info badge-sm me-1 mb-1 fw-semibold" v-for="dente in item.dentes" :key="dente">
                              {{ dente }}
                            </span>
                          </div>
                        </div>

                        <!-- Informações em formato vertical -->
                        <div class="item-info mb-3">
                          <div class="row g-2 text-center">
                            <div class="col-6">
                              <small class="text-uppercase fw-bold text-muted d-block mb-1" style="font-size: 0.65rem; letter-spacing: 0.5px;">Quantidade</small>
                              <span class="fw-bold text-dark" style="font-size: 1rem;">{{ item.quantidade }}</span>
                            </div>
                            <div class="col-6">
                              <small class="text-uppercase fw-bold text-muted d-block mb-1" style="font-size: 0.65rem; letter-spacing: 0.5px;">Valor Unit.</small>
                              <span class="fw-bold text-dark" style="font-size: 1rem;">{{ formatCurrency(item.valor_unitario) }}</span>
                            </div>
                            <div v-if="item.desconto_percentual > 0" class="col-12">
                              <small class="text-uppercase fw-bold text-muted d-block mb-1" style="font-size: 0.65rem; letter-spacing: 0.5px;">Desconto</small>
                              <span class="text-warning fw-bold" style="font-size: 1rem;">{{ item.desconto_percentual }}%</span>
                            </div>
                          </div>
                        </div>

                        <!-- Status Badge e Valor Total Centralizados -->
                        <div class="text-center mb-3">
                          <div class="mb-2">
                            <span class="badge fw-semibold" style="font-size: 0.75rem; padding: 0.4rem 0.8rem;" :class="getItemApprovalBadgeClass(item.status_aprovacao || 'pendente')">
                              {{ getItemApprovalText(item.status_aprovacao || 'pendente') }}
                            </span>
                          </div>
                          <div class="mb-1">
                            <small class="text-uppercase fw-bold text-muted d-block" style="font-size: 0.65rem; letter-spacing: 0.5px;">Valor Total</small>
                          </div>
                          <div class="item-total-value fw-bold" style="font-size: 1.5rem;" :class="getItemValueColorClass(item.status_aprovacao || 'pendente')">
                            {{ formatCurrency(calculateItemTotal(item)) }}
                          </div>
                        </div>

                        <!-- Observações (se houver) -->
                        <div v-if="item.observacoes" class="mb-3 p-2 bg-light rounded">
                          <small class="text-muted" style="font-size: 0.8rem;">
                            <i class="bi bi-sticky me-1"></i>{{ item.observacoes }}
                          </small>
                        </div>
                      </div>
                    </div>

                    <!-- Slot fantasma para adicionar novo item -->
                    <div class="col-12 col-md-6 col-lg-4" v-if="viewMode === 'creating-orcamento'">
                      <div
                        class="item-card-add p-3 border rounded-3 h-100 d-flex flex-column align-items-center justify-content-center"
                        :class="{ 'disabled': !orcamentoIniciado }"
                        @click="orcamentoIniciado ? addOrcamentoItem() : null"
                        role="button"
                        :tabindex="orcamentoIniciado ? 0 : -1"
                      >
                        <div class="add-icon mb-2">
                          <i class="fas fa-plus"></i>
                        </div>
                        <div class="add-text text-center">
                          <h6 class="mb-1">Adicionar item</h6>
                          <p class="mb-0 small text-muted">{{ orcamentoIniciado ? 'Novo procedimento' : 'Inicie o orçamento primeiro' }}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Resumo Unificado: Aprovação + Valores -->
                <div v-if="getItensParaExibir().length > 0" class="unified-summary mt-2 p-2 bg-white rounded-3 border shadow-sm">
                  <div class="row g-2">
                    <!-- Total Geral -->
                    <div class="col-6 col-lg-3">
                      <div class="summary-item text-center">
                        <div class="summary-badge bg-primary mb-2">
                          {{ getItensParaExibir().length }}
                        </div>
                        <div class="summary-label">Total Geral</div>
                        <div class="summary-value text-primary fw-bold">
                          {{ formatCurrency(getValorTotalResumo()) }}
                        </div>
                      </div>
                    </div>

                    <!-- Aprovados -->
                    <div class="col-6 col-lg-3">
                      <div class="summary-item text-center">
                        <div class="summary-badge bg-success mb-2">
                          {{ getItensAprovadosResumo().length }}
                        </div>
                        <div class="summary-label">Aprovados</div>
                        <div class="summary-value text-success">
                          {{ formatCurrency(getTotalItensAprovadosResumo()) }}
                        </div>
                      </div>
                    </div>

                    <!-- Rejeitados -->
                    <div class="col-6 col-lg-3">
                      <div class="summary-item text-center">
                        <div class="summary-badge bg-danger mb-2">
                          {{ getItensRejeitadosResumo().length }}
                        </div>
                        <div class="summary-label">Rejeitados</div>
                        <div class="summary-value text-danger">
                          {{ formatCurrency(getTotalItensRejeitadosResumo()) }}
                        </div>
                      </div>
                    </div>

                    <!-- Pendentes -->
                    <div class="col-6 col-lg-3">
                      <div class="summary-item text-center">
                        <div class="summary-badge bg-warning mb-2">
                          {{ getItensPendentesResumo().length }}
                        </div>
                        <div class="summary-label">Pendentes</div>
                        <div class="summary-value text-warning">
                          {{ formatCurrency(getTotalItensPendentesResumo()) }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Conteúdo: Valores e Detalhes da Fatura (Modo Criando Fatura) - Coluna Direita -->
              <div v-else-if="viewMode === 'creating-fatura'" class="fatura-campos-section p-3">
                <div class="fatura-valores-detalhes">
                  <!-- Valores, Vencimento e Ajustes -->
                  <div class="form-section">
                    <div class="section-header">
                      <i class="fas fa-calculator me-2"></i>
                      <span>Valores e Ajustes</span>
                    </div>

                    <!-- Container com max-width para os inputs -->
                    <div class="fatura-inputs-container mx-auto" style="max-width: 500px;">
                      <div class="row g-3">
                        <!-- Valor Nominal -->
                        <div class="col-md-6">
                          <label class="form-label elegant-label">Valor Nominal *</label>
                          <div class="elegant-input-group large-input-group" :class="{ 'is-invalid': faturaErrors.valor_nominal }">
                            <span class="elegant-addon elegant-addon-left">R$</span>
                            <input type="text"
                                   class="elegant-input money-input text-center"
                                   :class="{ 'is-invalid': faturaErrors.valor_nominal }"
                                   :value="formatCurrencyInput(novaFaturaForm.valor_nominal)"
                                   @input="updateValorNominal($event.target.value)"
                                   placeholder="0,00"
                                   maxlength="15">
                          </div>
                          <div class="invalid-feedback" v-if="faturaErrors.valor_nominal">
                            {{ faturaErrors.valor_nominal }}
                          </div>
                        </div>

                        <!-- Data de Vencimento -->
                        <div class="col-md-6">
                          <label class="form-label elegant-label">Vencimento *</label>
                          <input type="date"
                                 class="form-control elegant-input large-input text-center"
                                 v-model="novaFaturaForm.data_vencimento"
                                 :class="{ 'is-invalid': faturaErrors.data_vencimento }">
                          <div class="invalid-feedback" v-if="faturaErrors.data_vencimento">
                            {{ faturaErrors.data_vencimento }}
                          </div>
                        </div>

                        <!-- Parcelas -->
                        <div class="col-md-5">
                          <label class="form-label elegant-label text-center">Parcelas</label>
                          <div class="elegant-input-group" :class="{ 'is-invalid': faturaErrors.parcelas_total }">
                            <input type="number"
                                   min="1"
                                   max="60"
                                   class="elegant-input text-center"
                                   v-model.number="novaFaturaForm.parcelas_total">
                            <span class="elegant-addon elegant-addon-right">X</span>
                          </div>
                          <div class="invalid-feedback" v-if="faturaErrors.parcelas_total">
                            {{ faturaErrors.parcelas_total }}
                          </div>
                        </div>

                        <!-- Valor por Parcela -->
                        <div class="col-md-7">
                          <label class="form-label elegant-label">Valor/Parcela</label>
                          <div class="elegant-input-group">
                            <span class="elegant-addon elegant-addon-left">R$</span>
                            <input type="text"
                                   class="elegant-input text-center"
                                   :value="formatCurrency(valorPorParcelaFatura)"
                                   readonly>
                          </div>
                        </div>

                        <!-- Descontos -->
                        <div class="col-md-5">
                          <label class="form-label elegant-label text-center">Desconto %</label>
                          <div class="elegant-input-group" :class="{ 'field-active-group': activeDescontoField === 'percent' }">
                            <input type="number"
                                   min="0"
                                   max="100"
                                   step="0.01"
                                   class="elegant-input percent-input text-center"
                                   v-model.number="novaFaturaForm.percentual_desconto"
                                   @focus="activeDescontoField = 'percent'"
                                   @input="clearDescontoValue"
                                   placeholder="0">
                            <span class="elegant-addon elegant-addon-right">%</span>
                          </div>
                        </div>
                        <div class="col-md-7">
                          <label class="form-label elegant-label">Desconto R$</label>
                          <div class="elegant-input-group" :class="{ 'field-active-group': activeDescontoField === 'value' }">
                            <span class="elegant-addon elegant-addon-left">R$</span>
                            <input type="text"
                                   class="elegant-input money-input text-center"
                                   :value="formatCurrencyInput(novaFaturaForm.valor_desconto)"
                                   @input="updateValorDesconto($event.target.value)"
                                   @focus="activeDescontoField = 'value'"
                                   placeholder="0,00"
                                   maxlength="15">
                          </div>
                        </div>

                        <!-- Acréscimos -->
                        <div class="col-md-5">
                          <label class="form-label elegant-label text-center">Acréscimo %</label>
                          <div class="elegant-input-group" :class="{ 'field-active-group': activeAcrescimoField === 'percent' }">
                            <input type="number"
                                   min="0"
                                   max="100"
                                   step="0.01"
                                   class="elegant-input percent-input text-center"
                                   v-model.number="novaFaturaForm.percentual_acrescimo"
                                   @focus="activeAcrescimoField = 'percent'"
                                   @input="clearAcrescimoValue"
                                   placeholder="0">
                            <span class="elegant-addon elegant-addon-right">%</span>
                          </div>
                        </div>
                        <div class="col-md-7">
                          <label class="form-label elegant-label">Acréscimo R$</label>
                          <div class="elegant-input-group" :class="{ 'field-active-group': activeAcrescimoField === 'value' }">
                            <span class="elegant-addon elegant-addon-left">R$</span>
                            <input type="text"
                                   class="elegant-input money-input text-center"
                                   :value="formatCurrencyInput(novaFaturaForm.valor_acrescimo)"
                                   @input="updateValorAcrescimo($event.target.value)"
                                   @focus="activeAcrescimoField = 'value'"
                                   placeholder="0,00"
                                   maxlength="15">
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Botões de Ação - Lado a lado -->
                  <div class="row g-2 mt-4 mx-auto" style="max-width: 500px;">
                    <div class="col-6">
                      <button class="btn btn-success btn-lg w-100" @click="saveFatura" :disabled="saving">
                        <span v-if="saving">
                          <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                          Salvando...
                        </span>
                        <span v-else>
                          <i class="fas fa-save me-2"></i>Salvar
                        </span>
                      </button>
                    </div>
                    <div class="col-6">
                      <button class="btn btn-outline-secondary btn-lg w-100" @click="cancelCreation" :disabled="saving">
                        <i class="fas fa-times me-2"></i>Cancelar
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Conteúdo: Itens do Orçamento (Modo Visualizando Orçamento) -->
              <div v-else-if="viewMode === 'viewing-orcamento' && orcamentoSelecionado" class="orcamento-itens-view-section p-3">

                <!-- Grid de Itens (sempre visível) -->
                <div class="itens-grid">
                  <div class="row g-3">
                    <div v-for="(item, index) in getItensParaExibir()" :key="index" class="col-12 col-md-6 col-lg-4">
                      <div class="item-card-grid p-3 bg-white border rounded-3 shadow-sm h-100 position-relative" :class="getItemStatusClass(item)">
                        <!-- Botões de edição/exclusão (aparecem no hover) -->
                        <div v-if="canEditOrcamento(orcamentoSelecionado)" class="item-actions position-absolute top-0 end-0 p-2">
                          <div class="d-flex gap-1">
                            <button class="btn btn-sm btn-outline-primary px-2 py-1" @click="editOrcamentoItemInView(item)" title="Editar">
                              <i class="fas fa-edit" style="font-size: 0.75rem;"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger px-2 py-1" @click="removeOrcamentoItemInView(item)" title="Remover">
                              <i class="fas fa-trash" style="font-size: 0.75rem;"></i>
                            </button>
                          </div>
                        </div>

                        <!-- Header com nome -->
                        <div class="d-flex justify-content-between align-items-start mb-2">
                          <h6 class="mb-0 text-primary fw-bold flex-grow-1" style="font-size: 1rem;">{{ item.nome || 'Item sem nome' }}</h6>
                        </div>

                        <!-- Descrição (se houver) -->
                        <div v-if="item.descricao" class="mb-3">
                          <p class="text-muted mb-0" style="font-size: 0.875rem; line-height: 1.5;">{{ item.descricao }}</p>
                        </div>

                        <!-- Dentes (se houver) -->
                        <div v-if="item.dentes && item.dentes.length > 0" class="mb-3">
                          <small class="text-uppercase fw-bold text-muted d-block mb-2" style="font-size: 0.7rem; letter-spacing: 0.5px;">Dentes:</small>
                          <div class="dentes-badges">
                            <span class="badge bg-info badge-sm me-1 mb-1 fw-semibold" v-for="dente in item.dentes" :key="dente">
                              {{ dente }}
                            </span>
                          </div>
                        </div>

                        <!-- Informações em formato vertical -->
                        <div class="item-info mb-3">
                          <div class="row g-2 text-center">
                            <div class="col-6">
                              <small class="text-uppercase fw-bold text-muted d-block mb-1" style="font-size: 0.65rem; letter-spacing: 0.5px;">Quantidade</small>
                              <span class="fw-bold text-dark" style="font-size: 1rem;">{{ item.quantidade }}</span>
                            </div>
                            <div class="col-6">
                              <small class="text-uppercase fw-bold text-muted d-block mb-1" style="font-size: 0.65rem; letter-spacing: 0.5px;">Valor Unit.</small>
                              <span class="fw-bold text-dark" style="font-size: 1rem;">{{ formatCurrency(item.valor_unitario) }}</span>
                            </div>
                            <div v-if="item.desconto_percentual > 0" class="col-12">
                              <small class="text-uppercase fw-bold text-muted d-block mb-1" style="font-size: 0.65rem; letter-spacing: 0.5px;">Desconto</small>
                              <span class="text-warning fw-bold" style="font-size: 1rem;">{{ item.desconto_percentual }}%</span>
                            </div>
                          </div>
                        </div>

                        <!-- Status Badge e Valor Total Centralizados -->
                        <div class="text-center mb-3">
                          <div class="mb-2">
                            <span class="badge fw-semibold" style="font-size: 0.75rem; padding: 0.4rem 0.8rem;" :class="getItemApprovalBadgeClass(item.status_aprovacao || 'pendente')">
                              {{ getItemApprovalText(item.status_aprovacao || 'pendente') }}
                            </span>
                          </div>
                          <div class="mb-1">
                            <small class="text-uppercase fw-bold text-muted d-block" style="font-size: 0.65rem; letter-spacing: 0.5px;">Valor Total</small>
                          </div>
                          <div class="item-total-value fw-bold" style="font-size: 1.5rem;" :class="getItemValueColorClass(item.status_aprovacao || 'pendente')">
                            {{ formatCurrency(calculateItemTotal(item)) }}
                          </div>
                        </div>

                        <!-- Observações (se houver) -->
                        <div v-if="item.observacoes" class="mb-3 p-2 bg-light rounded">
                          <small class="text-muted" style="font-size: 0.8rem;">
                            <i class="bi bi-sticky me-1"></i>{{ item.observacoes }}
                          </small>
                        </div>

                        <!-- Controles de aprovação compactos -->
                        <div v-if="canManageItemApproval(orcamentoSelecionado)" class="mt-auto">
                          <div class="d-flex justify-content-center gap-2">
                            <button
                              class="btn btn-sm px-3 py-2"
                              :class="(item.status_aprovacao || 'pendente') === 'aprovado' ? 'btn-success' : 'btn-outline-success'"
                              @click="aprovarItem(item)"
                              title="Aprovar"
                            >
                              <i class="fas fa-check"></i>
                            </button>
                            <button
                              class="btn btn-sm px-3 py-2"
                              :class="(item.status_aprovacao || 'pendente') === 'rejeitado' ? 'btn-danger' : 'btn-outline-danger'"
                              @click="rejeitarItem(item)"
                              title="Rejeitar"
                            >
                              <i class="fas fa-times"></i>
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- Slot fantasma para adicionar novo item -->
                    <div class="col-12 col-md-6 col-lg-4" v-if="canEditOrcamento(orcamentoSelecionado)">
                      <div
                        class="item-card-add p-3 border rounded-3 h-100 d-flex flex-column align-items-center justify-content-center"
                        @click="addOrcamentoItem"
                        role="button"
                        tabindex="0"
                      >
                        <div class="add-icon mb-2">
                          <i class="fas fa-plus"></i>
                        </div>
                        <div class="add-text text-center">
                          <h6 class="mb-1">Adicionar item</h6>
                          <p class="mb-0 small text-muted">Novo procedimento</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Botões de ação em massa -->
                  <div v-if="canManageItemApproval(orcamentoSelecionado) && getItensParaExibir().length > 0" class="d-flex justify-content-center gap-3 mt-4 mb-3">
                    <button class="btn btn-success btn-sm px-4" @click="aprovarTodosItens">
                      <i class="fas fa-check-double me-2"></i>Aprovar Todos
                    </button>
                    <button class="btn btn-danger btn-sm px-4" @click="rejeitarTodosItens">
                      <i class="fas fa-times-circle me-2"></i>Rejeitar Todos
                    </button>
                  </div>
                </div>

                <!-- Resumo Unificado: Aprovação + Valores -->
                <div v-if="getItensParaExibir().length > 0" class="unified-summary mt-2 p-2 bg-white rounded-3 border shadow-sm">
                  <div class="row g-2">
                    <!-- Total Geral -->
                    <div class="col-6 col-lg-3">
                      <div class="summary-item text-center">
                        <div class="summary-badge bg-primary mb-2">
                          {{ getItensParaExibir().length }}
                        </div>
                        <div class="summary-label">Total Geral</div>
                        <div class="summary-value text-primary fw-bold">
                          {{ formatCurrency(getValorTotalResumo()) }}
                        </div>
                      </div>
                    </div>

                    <!-- Aprovados -->
                    <div class="col-6 col-lg-3">
                      <div class="summary-item text-center">
                        <div class="summary-badge bg-success mb-2">
                          {{ getItensAprovadosResumo().length }}
                        </div>
                        <div class="summary-label">Aprovados</div>
                        <div class="summary-value text-success">
                          {{ formatCurrency(getTotalItensAprovadosResumo()) }}
                        </div>
                      </div>
                    </div>

                    <!-- Rejeitados -->
                    <div class="col-6 col-lg-3">
                      <div class="summary-item text-center">
                        <div class="summary-badge bg-danger mb-2">
                          {{ getItensRejeitadosResumo().length }}
                        </div>
                        <div class="summary-label">Rejeitados</div>
                        <div class="summary-value text-danger">
                          {{ formatCurrency(getTotalItensRejeitadosResumo()) }}
                        </div>
                      </div>
                    </div>

                    <!-- Pendentes -->
                    <div class="col-6 col-lg-3">
                      <div class="summary-item text-center">
                        <div class="summary-badge bg-warning mb-2">
                          {{ getItensPendentesResumo().length }}
                        </div>
                        <div class="summary-label">Pendentes</div>
                        <div class="summary-value text-warning">
                          {{ formatCurrency(getTotalItensPendentesResumo()) }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    </div>

    <!-- Overlay para Edição de Item do Orçamento -->
    <div v-if="showItemEditor" class="item-editor-overlay" @click.self="closeItemEditor">
      <div class="item-editor-panel">
        <div class="item-editor-header-compact">
          <div>
            <h6 class="mb-0">
              <i class="fas fa-tooth me-2"></i>
              {{ editingItemIndex !== null ? 'Editar Item' : 'Novo Item' }}
            </h6>
          </div>
          <button type="button" class="btn btn-sm btn-light" @click="closeItemEditor">
            <i class="fas fa-times"></i>
          </button>
        </div>

        <div class="item-editor-body">
          <!-- Seletor de Dentes com Serviço no Topo -->
          <div class="dente-selector-container">
            <!-- Serviço/Procedimento dentro do container -->
            <div class="mb-3">
              <label class="form-label fw-bold">Serviço/Procedimento *</label>
              <select class="form-select"
                      v-model="itemForm.servico_produto_id"
                      @change="onServicoChange"
                      :class="{ 'is-invalid': itemErrors.servico_produto_id }">
                <option :value="null" disabled selected>Selecione um serviço ({{ servicosProdutos.length }} disponíveis)</option>
                <option v-for="servico in servicosProdutos"
                        :key="servico.id"
                        :value="servico.id">
                  {{ servico.nome }}
                </option>
              </select>
              <div v-if="itemErrors.servico_produto_id" class="invalid-feedback">
                {{ itemErrors.servico_produto_id }}
              </div>
            </div>

            <!-- Seletor de Dentes -->
            <dente-selector v-model="itemForm.dentes" @update:modelValue="updateQuantidadeFromDentes" />
          </div>

          <!-- Campos Compactados -->
          <div class="row g-2 mt-3">
            <!-- Quantidade -->
            <div class="col-3">
              <label class="form-label small fw-bold">Quantidade *</label>
              <input type="number"
                     step="0.01"
                     min="0.01"
                     class="form-control item-input-compact text-center"
                     v-model.number="itemForm.quantidade"
                     :class="{ 'is-invalid': itemErrors.quantidade }">
              <div v-if="itemErrors.quantidade" class="invalid-feedback">
                {{ itemErrors.quantidade }}
              </div>
            </div>

            <!-- Valor Unitário -->
            <div class="col-3">
              <label class="form-label small fw-bold">Valor Unit. *</label>
              <div class="input-group item-input-group-compact">
                <span class="input-group-text">R$</span>
                <input type="number"
                       step="0.01"
                       min="0.01"
                       class="form-control text-end"
                       v-model.number="itemForm.valor_unitario"
                       :class="{ 'is-invalid': itemErrors.valor_unitario }">
              </div>
              <div v-if="itemErrors.valor_unitario" class="invalid-feedback">
                {{ itemErrors.valor_unitario }}
              </div>
            </div>

            <!-- Desconto -->
            <div class="col-3">
              <label class="form-label small fw-bold">Desc. (%)</label>
              <input type="number"
                     step="0.01"
                     min="0"
                     max="100"
                     class="form-control item-input-compact text-center"
                     v-model.number="itemForm.desconto_percentual"
                     placeholder="0">
            </div>

            <!-- Total Calculado -->
            <div class="col-3">
              <label class="form-label small fw-bold">Total</label>
              <div class="item-total-display">
                <strong>{{ formatCurrency(calculateItemTotal(itemForm)) }}</strong>
              </div>
            </div>

            <!-- Observações -->
            <div class="col-12">
              <label class="form-label small">Observações</label>
              <textarea class="form-control form-control-sm"
                        rows="2"
                        v-model="itemForm.observacoes"
                        placeholder="Observações específicas deste item"></textarea>
            </div>
          </div>
        </div>

        <div class="item-editor-footer">
          <button type="button" class="btn btn-light btn-sm" @click="closeItemEditor">
            <i class="fas fa-times me-2"></i>
            Cancelar
          </button>
          <button type="button"
                  class="btn btn-primary btn-sm"
                  @click="saveItem"
                  :disabled="savingItem">
            <i class="fas fa-save me-2"></i>
            {{ savingItem ? 'Salvando...' : 'Salvar Item' }}
          </button>
        </div>
      </div>
    </div>
</template>

<script>
import { financeiroService } from '@/services/financeiroService';
import { getDentistas } from '@/services/dentistasService';
import { servicoProdutoService } from '@/services/servicoProdutoService';
import FaturaFormElegant from './FaturaFormElegant.vue';
import DenteSelector from './DenteSelector.vue';

export default {
  name: 'PacienteFinanceiro',
  components: {
    FaturaFormElegant,
    DenteSelector
  },
  props: {
    paciente: {
      type: Object,
      required: true
    },
    faturas: {
      type: Array,
      default: () => []
    },
    orcamentos: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Object,
      default: () => ({
        faturas: false,
        orcamentos: false
      })
    }
  },
  data() {
    return {
      filters: {
        statusList: ['pago', 'pendente', 'vencido', 'cancelado'], // Todos ativos por padrão
        descricao: ''
      },
      // Estados de criação
      viewMode: 'normal', // 'normal', 'creating-orcamento', 'creating-fatura', 'viewing-orcamento'
      saving: false, // Estado de salvamento
      dentistas: [], // Lista de dentistas
      servicosProdutos: [], // Lista de serviços/produtos

      // Orçamento sendo visualizado
      orcamentoSelecionado: null,
      generatingPDF: false,
      editingOrcamento: false, // Flag para indicar se está editando um orçamento existente
      activeDropdown: null, // ID do dropdown ativo
      orcamentoIniciado: false, // Flag para indicar se o orçamento foi iniciado (criado no backend)

      // Formulário de Novo Orçamento
      novoOrcamentoForm: {
        paciente_id: '',
        dentista_id: '',
        titulo: '',
        descricao: '',
        data_validade: '',
        observacoes: '',
        itens: []
      },
      orcamentoErrors: {},

      // Editor de item do orçamento
      showItemEditor: false,
      editingItemIndex: null,
      savingItem: false,
      itemForm: {
        servico_produto_id: null,
        nome: '',
        descricao: '',
        quantidade: 1,
        valor_unitario: 0,
        desconto_percentual: 0,
        desconto_valor: 0,
        observacoes: '',
        dentes: []
      },
      itemErrors: {},

      // Formulário de Nova Fatura
      novaFaturaForm: {
        paciente_id: '',
        dentista_id: '',
        descricao: '',
        valor_nominal: '',
        data_vencimento: '',
        parcelas_total: 1,
        percentual_desconto: '',
        valor_desconto: '',
        percentual_acrescimo: '',
        valor_acrescimo: '',
        observacoes: ''
      },
      faturaErrors: {},

      // Controle de campos ativos (para highlight)
      activeDescontoField: null, // 'percent' ou 'value'
      activeAcrescimoField: null, // 'percent' ou 'value'

      // Estados para edição inline do orçamento
      editingOrcamentoTitle: false,
      editingTitleValue: '',
      editingOrcamentoDescription: false,
      editingDescriptionValue: '',
      editingOrcamentoDate: false,
      editingDateValue: ''
    };
  },
  computed: {
    filteredFaturas() {
      let filtered = [...this.faturas];

      // Filtrar por status (se algum status estiver selecionado)
      if (this.filters.statusList.length > 0) {
        filtered = filtered.filter(fatura => this.filters.statusList.includes(fatura.status));
      }

      // Filtrar por descrição
      if (this.filters.descricao) {
        filtered = filtered.filter(fatura =>
          fatura.descricao.toLowerCase().includes(this.filters.descricao.toLowerCase())
        );
      }

      return filtered.sort((a, b) => new Date(b.data_vencimento) - new Date(a.data_vencimento));
    },

    totalPago() {
      return this.faturas
        .filter(f => f.status === 'pago')
        .reduce((sum, f) => sum + parseFloat(f.valor_final || 0), 0);
    },

    totalPendente() {
      return this.faturas
        .filter(f => f.status === 'pendente')
        .reduce((sum, f) => sum + parseFloat(f.valor_final || 0), 0);
    },

    totalVencido() {
      return this.faturas
        .filter(f => f.status === 'vencido' || this.isOverdue(f))
        .reduce((sum, f) => sum + parseFloat(f.valor_final || 0), 0);
    },

    totalCancelado() {
      return this.faturas
        .filter(f => f.status === 'cancelado')
        .reduce((sum, f) => sum + parseFloat(f.valor_final || 0), 0);
    },

    totalGeral() {
      return this.faturas
        .reduce((sum, f) => sum + parseFloat(f.valor_final || 0), 0);
    },

    totalOrcamento() {
      return this.novoOrcamentoForm.itens.reduce((sum, item) => {
        return sum + this.calculateItemTotal(item);
      }, 0);
    },

    valorFinalFatura() {
      let valor = parseFloat(this.novaFaturaForm.valor_nominal) || 0;

      // Aplicar desconto
      if (this.novaFaturaForm.valor_desconto) {
        valor -= parseFloat(this.novaFaturaForm.valor_desconto);
      } else if (this.novaFaturaForm.percentual_desconto) {
        valor -= (valor * parseFloat(this.novaFaturaForm.percentual_desconto)) / 100;
      }

      // Aplicar acréscimo
      if (this.novaFaturaForm.valor_acrescimo) {
        valor += parseFloat(this.novaFaturaForm.valor_acrescimo);
      } else if (this.novaFaturaForm.percentual_acrescimo) {
        valor += (valor * parseFloat(this.novaFaturaForm.percentual_acrescimo)) / 100;
      }

      return Math.max(0, valor);
    },

    valorPorParcelaFatura() {
      const parcelas = this.novaFaturaForm.parcelas_total || 1;
      return this.valorFinalFatura / parcelas;
    },

    // ========== COMPUTED PARA APROVAÇÃO DE ITENS ==========
    itensAprovados() {
      if (!this.orcamentoSelecionado?.itens) return [];
      return this.orcamentoSelecionado.itens.filter(item =>
        (item.status_aprovacao || 'pendente') === 'aprovado'
      );
    },

    itensRejeitados() {
      if (!this.orcamentoSelecionado?.itens) return [];
      return this.orcamentoSelecionado.itens.filter(item =>
        (item.status_aprovacao || 'pendente') === 'rejeitado'
      );
    },

    itensPendentes() {
      if (!this.orcamentoSelecionado?.itens) return [];
      return this.orcamentoSelecionado.itens.filter(item =>
        (item.status_aprovacao || 'pendente') === 'pendente'
      );
    },

    totalItensAprovados() {
      return this.itensAprovados.reduce((sum, item) => {
        return sum + (parseFloat(item.quantidade || 0) * parseFloat(item.valor_unitario || 0));
      }, 0);
    },

    totalItensRejeitados() {
      return this.itensRejeitados.reduce((sum, item) => {
        return sum + (parseFloat(item.quantidade || 0) * parseFloat(item.valor_unitario || 0));
      }, 0);
    },

    totalItensPendentes() {
      return this.itensPendentes.reduce((sum, item) => {
        return sum + (parseFloat(item.quantidade || 0) * parseFloat(item.valor_unitario || 0));
      }, 0);
    },

    // ========== COMPUTED PARA NOVO ORÇAMENTO (MODO CRIANDO) ==========
    itensAprovadosNovoOrcamento() {
      if (!this.novoOrcamentoForm?.itens) return [];
      return this.novoOrcamentoForm.itens.filter(item =>
        (item.status_aprovacao || 'pendente') === 'aprovado'
      );
    },

    itensRejeitadosNovoOrcamento() {
      if (!this.novoOrcamentoForm?.itens) return [];
      return this.novoOrcamentoForm.itens.filter(item =>
        (item.status_aprovacao || 'pendente') === 'rejeitado'
      );
    },

    itensPendentesNovoOrcamento() {
      if (!this.novoOrcamentoForm?.itens) return [];
      return this.novoOrcamentoForm.itens.filter(item =>
        (item.status_aprovacao || 'pendente') === 'pendente'
      );
    },

    totalItensAprovadosNovoOrcamento() {
      return this.itensAprovadosNovoOrcamento.reduce((sum, item) => {
        return sum + (parseFloat(item.quantidade || 0) * parseFloat(item.valor_unitario || 0));
      }, 0);
    },

    totalItensRejeitadosNovoOrcamento() {
      return this.itensRejeitadosNovoOrcamento.reduce((sum, item) => {
        return sum + (parseFloat(item.quantidade || 0) * parseFloat(item.valor_unitario || 0));
      }, 0);
    },

    totalItensPendentesNovoOrcamento() {
      return this.itensPendentesNovoOrcamento.reduce((sum, item) => {
        return sum + (parseFloat(item.quantidade || 0) * parseFloat(item.valor_unitario || 0));
      }, 0);
    },

    // Verifica se há itens aprovados no orçamento
    hasItensAprovados() {
      if (this.viewMode === 'creating-orcamento') {
        return this.novoOrcamentoForm.itens?.some(item =>
          (item.status_aprovacao || 'pendente') === 'aprovado'
        ) || false;
      }
      if (this.viewMode === 'viewing-orcamento' && this.orcamentoSelecionado) {
        return this.orcamentoSelecionado.itens?.some(item =>
          (item.status_aprovacao || 'pendente') === 'aprovado'
        ) || false;
      }
      return false;
    }
  },



  methods: {
    formatCurrency: financeiroService.formatCurrency,
    formatDate: financeiroService.formatDate,
    getStatusBadgeClass: financeiroService.getStatusBadgeClass,
    getStatusText: financeiroService.getStatusText,

    // ========== MÉTODOS AUXILIARES DE STATUS ORÇAMENTO ==========
    getOrcamentoStatusBadgeClass(status) {
      const statusClasses = {
        'rascunho': 'bg-secondary',
        'pendente': 'bg-warning text-dark',
        'enviado': 'bg-info',
        'aprovado': 'bg-success',
        'rejeitado': 'bg-danger',
        'expirado': 'bg-warning text-dark',
        'convertido': 'bg-primary',
        'efetivado': 'bg-success'
      };
      return statusClasses[status] || 'bg-secondary';
    },

    getOrcamentoStatusText(status) {
      const statusTexts = {
        'rascunho': 'Rascunho',
        'pendente': 'Pendente',
        'enviado': 'Enviado',
        'aprovado': 'Aprovado',
        'rejeitado': 'Rejeitado',
        'expirado': 'Expirado',
        'convertido': 'Convertido',
        'efetivado': 'Efetivado'
      };
      return statusTexts[status] || 'Desconhecido';
    },

    canEditOrcamento(orcamento) {
      // Pode editar se não estiver efetivado (convertido em fatura)
      return !['efetivado', 'convertido'].includes(orcamento.status);
    },

    canApproveOrcamento(orcamento) {
      const isEnviado = orcamento.status === 'enviado';
      const naoExpirado = !orcamento.data_validade || new Date(orcamento.data_validade) >= new Date();
      return isEnviado && naoExpirado;
    },

    canConvertOrcamento(orcamento) {
      return orcamento.status === 'aprovado';
    },

    // ========== MÉTODOS AUXILIARES DE APROVAÇÃO DE ITENS ==========
    getItemApprovalBadgeClass(status) {
      const statusClasses = {
        'pendente': 'bg-warning text-dark',
        'aprovado': 'bg-success text-white',
        'rejeitado': 'bg-danger text-white'
      };
      return statusClasses[status] || 'bg-warning text-dark';
    },

    getItemApprovalText(status) {
      const statusTexts = {
        'pendente': 'Pendente',
        'aprovado': 'Aprovado',
        'rejeitado': 'Rejeitado'
      };
      return statusTexts[status] || 'Pendente';
    },

    getItemStatusClass(item) {
      const status = item.status_aprovacao || 'pendente';
      return {
        'border-success': status === 'aprovado',
        'border-danger': status === 'rejeitado',
        'border-warning': status === 'pendente'
      };
    },

    getItemValueColorClass(status) {
      const colorClasses = {
        'pendente': 'text-warning',
        'aprovado': 'text-success',
        'rejeitado': 'text-danger'
      };
      return colorClasses[status] || 'text-warning';
    },

    canManageItemApproval(orcamento) {
      // Pode gerenciar aprovação se o orçamento não estiver efetivado
      if (!orcamento) return false;
      return !['efetivado', 'convertido'].includes(orcamento.status);
    },

    isNovoOrcamento(orcamento) {
      // Detectar se é um novo orçamento (sem itens)
      if (!orcamento) return false;
      return (!orcamento.itens || orcamento.itens.length === 0) && orcamento.status === 'rascunho';
    },

    // ========== MÉTODOS AUXILIARES DE RESUMO DE VALORES ==========
    getValorTotalResumo() {
      // Retorna o valor total baseado no modo (novo orçamento ou visualizando)
      if (this.viewMode === 'creating-orcamento') {
        return this.totalOrcamento || 0;
      } else if (this.viewMode === 'viewing-orcamento' && this.orcamentoSelecionado) {
        return this.orcamentoSelecionado?.valor_total || 0;
      }
      return 0;
    },

    getValorAprovadoResumo() {
      // Retorna o valor aprovado baseado no modo (novo orçamento ou visualizando)
      if (this.viewMode === 'creating-orcamento') {
        return this.totalItensAprovadosNovoOrcamento || 0;
      } else if (this.viewMode === 'viewing-orcamento' && this.orcamentoSelecionado) {
        return this.totalItensAprovados || 0;
      }
      return 0;
    },

    // ========== MÉTODOS AUXILIARES DE RENDERIZAÇÃO REUTILIZÁVEL ==========
    getItensParaExibir() {
      // Retorna os itens baseado no modo (novo orçamento ou visualizando)
      if (this.viewMode === 'creating-orcamento') {
        return this.novoOrcamentoForm?.itens || [];
      } else if (this.viewMode === 'viewing-orcamento' && this.orcamentoSelecionado) {
        return this.orcamentoSelecionado?.itens || [];
      }
      return [];
    },

    getItensAprovadosResumo() {
      // Retorna itens aprovados baseado no modo
      if (this.viewMode === 'creating-orcamento') {
        return this.itensAprovadosNovoOrcamento || [];
      } else if (this.viewMode === 'viewing-orcamento') {
        return this.itensAprovados || [];
      }
      return [];
    },

    getItensRejeitadosResumo() {
      // Retorna itens rejeitados baseado no modo
      if (this.viewMode === 'creating-orcamento') {
        return this.itensRejeitadosNovoOrcamento || [];
      } else if (this.viewMode === 'viewing-orcamento') {
        return this.itensRejeitados || [];
      }
      return [];
    },

    getItensPendentesResumo() {
      // Retorna itens pendentes baseado no modo
      if (this.viewMode === 'creating-orcamento') {
        return this.itensPendentesNovoOrcamento || [];
      } else if (this.viewMode === 'viewing-orcamento') {
        return this.itensPendentes || [];
      }
      return [];
    },

    getTotalItensAprovadosResumo() {
      // Retorna total de itens aprovados baseado no modo
      if (this.viewMode === 'creating-orcamento') {
        return this.totalItensAprovadosNovoOrcamento || 0;
      } else if (this.viewMode === 'viewing-orcamento') {
        return this.totalItensAprovados || 0;
      }
      return 0;
    },

    getTotalItensRejeitadosResumo() {
      // Retorna total de itens rejeitados baseado no modo
      if (this.viewMode === 'creating-orcamento') {
        return this.totalItensRejeitadosNovoOrcamento || 0;
      } else if (this.viewMode === 'viewing-orcamento') {
        return this.totalItensRejeitados || 0;
      }
      return 0;
    },

    getTotalItensPendentesResumo() {
      // Retorna total de itens pendentes baseado no modo
      if (this.viewMode === 'creating-orcamento') {
        return this.totalItensPendentesNovoOrcamento || 0;
      } else if (this.viewMode === 'viewing-orcamento') {
        return this.totalItensPendentes || 0;
      }
      return 0;
    },

    async iniciarOrcamento() {
      // Validar campos obrigatórios
      if (!this.novoOrcamentoForm.titulo) {
        const cSwal = await import('@/utils/cSwal.js');
        cSwal.default.cError('Por favor, preencha o título do orçamento');
        return;
      }

      try {
        // Criar orçamento vazio no banco
        const { orcamentoService } = await import('@/services/orcamentoService');

        const orcamentoData = {
          paciente_id: this.paciente?.id || '',
          dentista_id: this.$dentista?.id || '',
          titulo: this.novoOrcamentoForm.titulo,
          descricao: this.novoOrcamentoForm.descricao || '',
          data_orcamento: new Date().toISOString().split('T')[0],
          status: 'rascunho'
          // Não enviar itens para permitir criar orçamento vazio
        };

        const response = await orcamentoService.createOrcamento(orcamentoData);
        const orcamentoCriado = response.data.data;

        // Marcar como iniciado e mudar para modo de visualização/edição com a interface polida
        this.orcamentoIniciado = true;
        this.orcamentoSelecionado = orcamentoCriado;
        this.viewMode = 'viewing-orcamento';
        this.editingOrcamento = false;
        this.resetOrcamentoForm();

        // Emitir evento para o componente pai recarregar a lista de orçamentos
        this.$emit('orcamento-updated', orcamentoCriado);

        const cSwal = await import('@/utils/cSwal.js');
        cSwal.default.cSuccess('Orçamento iniciado! Agora você pode adicionar itens.');
      } catch (error) {
        console.error('Erro ao iniciar orçamento:', error);
        const cSwal = await import('@/utils/cSwal.js');
        cSwal.default.cError('Erro ao iniciar orçamento: ' + (error.response?.data?.message || error.message));
      }
    },

    // ========== MÉTODOS DE ORÇAMENTO ==========
    toggleDropdown(orcamentoId) {
      this.activeDropdown = this.activeDropdown === orcamentoId ? null : orcamentoId;
    },

    async editOrcamento(orcamento) {
      this.activeDropdown = null; // Fechar dropdown

      try {
        // Carregar dados frescos do backend
        const { orcamentoService } = await import('@/services/orcamentoService');
        const response = await orcamentoService.getOrcamento(orcamento.id);
        const orcamentoFresco = response.data.data;



        // Carregar dados frescos no formulário de edição
        this.novoOrcamentoForm = {
          id: orcamentoFresco.id,
          paciente_id: orcamentoFresco.paciente_id || '',
          dentista_id: orcamentoFresco.dentista_id || '',
          titulo: orcamentoFresco.titulo || '',
          descricao: orcamentoFresco.descricao || '',
          data_validade: orcamentoFresco.data_validade || '',
          observacoes: orcamentoFresco.observacoes || '',
          itens: orcamentoFresco.itens ? [...orcamentoFresco.itens] : []
        };


      } catch (error) {
        console.error('Erro ao carregar dados frescos do orçamento:', error);
        const cSwal = await import('@/utils/cSwal.js');
        cSwal.default.cError('Erro ao carregar dados do orçamento');
        return;
      }

      // Mudar para modo de edição (reutiliza o modo de criação)
      this.viewMode = 'creating-orcamento';
      this.editingOrcamento = true;
      this.orcamentoIniciado = true; // Marcar como iniciado pois está editando um orçamento existente
      // Manter referência do orçamento que estava sendo visualizado
      // this.orcamentoSelecionado = orcamentoFresco; // Manter para poder voltar

      // Dar focus no input do título
      this.$nextTick(() => {
        if (this.$refs.tituloOrcamentoInput && window.innerWidth >= 768) {
          this.$refs.tituloOrcamentoInput.focus();
        }
      });
    },

    async gerarFaturaDoFormulario() {
      // Método para gerar fatura a partir do formulário de orçamento (modo creating-orcamento)
      await this.generateFaturaFromOrcamento(this.novoOrcamentoForm);
    },

    async generateFaturaFromOrcamento(orcamento) {
      this.activeDropdown = null; // Fechar dropdown

      // Verificar se há itens aprovados - SEMPRE GERAR APENAS COM ITENS APROVADOS
      const itensAprovados = orcamento.itens?.filter(item =>
        (item.status_aprovacao || 'pendente') === 'aprovado'
      ) || [];

      if (itensAprovados.length === 0) {
        const cSwal = await import('@/utils/cSwal.js');
        cSwal.default.cError('Não é possível gerar fatura sem itens aprovados. Por favor, aprove pelo menos um item do orçamento.');
        return;
      }

      // Calcular valor baseado APENAS nos itens aprovados
      const valorTotal = itensAprovados.reduce((sum, item) => {
        const valorItem = parseFloat(item.valor_total || 0) ||
                         (parseFloat(item.quantidade || 0) * parseFloat(item.valor_unitario || 0));
        return sum + valorItem;
      }, 0);

      // Preencher formulário da nova fatura com dados do orçamento
      this.novaFaturaForm = {
        paciente_id: orcamento.paciente_id || '',
        dentista_id: orcamento.dentista_id || '',
        descricao: `${orcamento.titulo || 'Fatura'} (${itensAprovados.length} ${itensAprovados.length === 1 ? 'item aprovado' : 'itens aprovados'})`,
        valor_nominal: valorTotal,
        data_vencimento: '',
        parcelas_total: 1,
        percentual_desconto: '',
        valor_desconto: '',
        percentual_acrescimo: '',
        valor_acrescimo: '',
        observacoes: orcamento.observacoes || ''
      };

      // Mudar para o modo de criação de fatura
      this.viewMode = 'creating-fatura';
    },

    async generateOrcamentoPDF(orcamento) {
      this.activeDropdown = null; // Fechar dropdown
      this.generatingPDF = true;
      try {
        // Importar o serviço de PDF
        const { generatePDF } = await import('@/services/pdfService');

        // Criar elemento HTML temporário com o orçamento
        const orcamentoElement = this.createOrcamentoHTML(orcamento);

        // Adicionar ao DOM temporariamente
        document.body.appendChild(orcamentoElement);

        // Gerar PDF
        const filename = `Orcamento_${orcamento.titulo || 'Orcamento'}_${new Date().toISOString().split('T')[0]}.pdf`;
        await generatePDF(orcamentoElement, { filename }, true);

        // Remover elemento temporário
        document.body.removeChild(orcamentoElement);

        // Mostrar sucesso
        const cSwal = await import('@/utils/cSwal.js');
        cSwal.default.cSuccess('PDF do orçamento gerado com sucesso!');
      } catch (error) {
        console.error('Erro ao gerar PDF:', error);
        const cSwal = await import('@/utils/cSwal.js');
        cSwal.default.cError('Erro ao gerar PDF do orçamento');
      } finally {
        this.generatingPDF = false;
      }
    },

    createOrcamentoHTML(orcamento) {
      const valorTotal = orcamento.itens ? orcamento.itens.reduce((sum, item) => {
        return sum + (item.quantidade * item.valor_unitario);
      }, 0) : 0;

      const container = document.createElement('div');
      container.style.padding = '20px';
      container.style.fontFamily = 'Arial, sans-serif';
      container.className = 'orcamento-print-view';

      // Criar CSS separadamente
      const styles = `
        <style>
          .orcamento-print-view {
            font-family: Arial, sans-serif;
            padding: 20px;
            max-width: 100%;
          }
          .print-title {
            text-align: center;
            color: #3f51b5;
            margin-bottom: 20px;
            font-size: 24px;
          }
          .print-info {
            margin-bottom: 30px;
          }
          .print-info table {
            width: 100%;
            border-collapse: collapse;
          }
          .print-info td {
            padding: 8px;
            border-bottom: 1px solid #eee;
          }
          .print-info td:first-child {
            font-weight: bold;
            width: 150px;
          }
          .itens-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
          }
          .itens-table th, .itens-table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #ddd;
          }
          .itens-table th {
            background-color: #f2f2f2;
            font-weight: bold;
          }
          .total-section {
            text-align: right;
            font-size: 18px;
            font-weight: bold;
            margin-top: 20px;
          }
          .print-footer {
            margin-top: 40px;
            text-align: center;
            color: #666;
            font-size: 12px;
          }
        </style>
      `;

      // Criar seção de informações
      const pacienteNome = orcamento.paciente?.nome || 'Não informado';
      const dataOrcamento = orcamento.data_orcamento ?
        new Date(orcamento.data_orcamento).toLocaleDateString('pt-BR') :
        new Date().toLocaleDateString('pt-BR');
      const dataValidade = orcamento.data_validade ?
        new Date(orcamento.data_validade).toLocaleDateString('pt-BR') :
        'Não informada';

      const descricaoRow = orcamento.descricao ?
        `<tr><td>Descrição:</td><td>${orcamento.descricao}</td></tr>` : '';

      const infoSection = `
        <div class="print-info">
          <table>
            <tr>
              <td>Paciente:</td>
              <td>${pacienteNome}</td>
            </tr>
            <tr>
              <td>Data:</td>
              <td>${dataOrcamento}</td>
            </tr>
            <tr>
              <td>Validade:</td>
              <td>${dataValidade}</td>
            </tr>
            ${descricaoRow}
          </table>
        </div>
      `;

      // Criar linhas dos itens
      const itensRows = (orcamento.itens || []).map(item => {
        const itemNome = item.nome || 'Item sem nome';
        const itemDescricao = item.descricao ?
          `<br><small style="color: #666;">${item.descricao}</small>` : '';
        const dentes = item.dentes && item.dentes.length > 0 ?
          item.dentes.sort((a, b) => a - b).join(', ') : '-';
        const quantidade = item.quantidade || 0;
        const valorUnitario = parseFloat(item.valor_unitario || 0).toFixed(2).replace('.', ',');
        const totalItem = (parseFloat(item.quantidade || 0) * parseFloat(item.valor_unitario || 0)).toFixed(2).replace('.', ',');

        // Status de aprovação
        const statusAprovacao = item.status_aprovacao || 'pendente';
        const statusTexts = {
          'pendente': 'Pendente',
          'aprovado': 'Aprovado',
          'rejeitado': 'Rejeitado'
        };
        const statusColors = {
          'pendente': '#6c757d',
          'aprovado': '#28a745',
          'rejeitado': '#dc3545'
        };
        const statusText = statusTexts[statusAprovacao] || 'Pendente';
        const statusColor = statusColors[statusAprovacao] || '#6c757d';

        const observacoesRow = item.observacoes ?
          `<tr><td colspan="6" style="padding: 5px 10px; font-size: 12px; color: #666; border-bottom: none;"><em>Obs: ${item.observacoes}</em></td></tr>` : '';

        return `
          <tr>
            <td><strong>${itemNome}</strong>${itemDescricao}</td>
            <td>${dentes}</td>
            <td>${quantidade}</td>
            <td>R$ ${valorUnitario}</td>
            <td>R$ ${totalItem}</td>
            <td style="color: ${statusColor}; font-weight: bold;">${statusText}</td>
          </tr>
          ${observacoesRow}
        `;
      }).join('');

      const itensTable = `
        <table class="itens-table">
          <thead>
            <tr>
              <th>Item</th>
              <th>Dentes</th>
              <th>Quantidade</th>
              <th>Valor Unitário</th>
              <th>Total</th>
              <th>Status</th>
            </tr>
          </thead>
          <tbody>
            ${itensRows}
          </tbody>
        </table>
      `;

      // Seção de total
      const valorFinalFormatado = parseFloat(orcamento.valor_final || valorTotal || 0).toFixed(2).replace('.', ',');
      const totalSection = `
        <div class="total-section">
          Total Geral: R$ ${valorFinalFormatado}
        </div>
      `;

      // Observações
      const observacoesSection = orcamento.observacoes ?
        `<div style="margin-top: 30px;"><strong>Observações:</strong><br>${orcamento.observacoes}</div>` : '';

      // Footer
      const dataAtual = new Date().toLocaleDateString('pt-BR');
      const horaAtual = new Date().toLocaleTimeString('pt-BR');
      const footer = `
        <div class="print-footer">
          <p>Gerado em ${dataAtual} às ${horaAtual}</p>
          <p>LUMI Vision</p>
        </div>
      `;

      // Montar HTML final
      const titulo = orcamento.titulo || 'Orçamento';
      container.innerHTML = styles +
        `<h1 class="print-title">${titulo}</h1>` +
        infoSection +
        itensTable +
        totalSection +
        observacoesSection +
        footer;

      return container;
    },

    // ========== MÉTODOS DE APROVAÇÃO DE ITENS ==========
    async aprovarItem(item) {
      try {
        const cSwal = await import('@/utils/cSwal.js');
        const result = await cSwal.default.cConfirm(
          'Deseja aprovar este item do orçamento?',
          'Aprovar Item'
        );

        if (result.isConfirmed) {
          // Fazer requisição para o backend
          const { orcamentoService } = await import('@/services/orcamentoService');
          const response = await orcamentoService.aprovarItem(
            this.orcamentoSelecionado.id,
            item.id
          );

          // Atualizar orçamento com dados do backend
          this.orcamentoSelecionado = response.data.data;

          cSwal.default.cSuccess('Item aprovado com sucesso!');
        }
      } catch (error) {
        console.error('Erro ao aprovar item:', error);
        const cSwal = await import('@/utils/cSwal.js');
        cSwal.default.cError('Erro ao aprovar item');
      }
    },

    async rejeitarItem(item) {
      try {
        const cSwal = await import('@/utils/cSwal.js');
        const { value: motivo } = await cSwal.default.cInput(
          'Motivo da rejeição (opcional):',
          'Rejeitar Item'
        );

        if (motivo !== undefined) { // undefined = cancelou, string = confirmou
          // Fazer requisição para o backend
          const { orcamentoService } = await import('@/services/orcamentoService');
          const response = await orcamentoService.rejeitarItem(
            this.orcamentoSelecionado.id,
            item.id,
            motivo || ''
          );

          // Atualizar orçamento com dados do backend
          this.orcamentoSelecionado = response.data.data;

          cSwal.default.cSuccess('Item rejeitado com sucesso!');
        }
      } catch (error) {
        console.error('Erro ao rejeitar item:', error);
        const cSwal = await import('@/utils/cSwal.js');
        cSwal.default.cError('Erro ao rejeitar item');
      }
    },

    async resetarAprovacaoItem(item) {
      try {
        const cSwal = await import('@/utils/cSwal.js');
        const result = await cSwal.default.cConfirm(
          'Deseja resetar a aprovação deste item?',
          'Resetar Aprovação'
        );

        if (result.isConfirmed) {
          // Atualizar localmente
          item.status_aprovacao = 'pendente';
          item.aprovado_em = null;
          item.aprovado_por = null;
          item.motivo_rejeicao = null;

          // Aqui você pode implementar a chamada para a API se necessário
          // await orcamentoService.resetarAprovacaoItem(item.id);

          cSwal.default.cSuccess('Aprovação resetada com sucesso!');
        }
      } catch (error) {
        console.error('Erro ao resetar aprovação:', error);
        const cSwal = await import('@/utils/cSwal.js');
        cSwal.default.cError('Erro ao resetar aprovação');
      }
    },

    // ========== MÉTODOS DE APROVAÇÃO EM MASSA ==========
    async aprovarTodosItens() {
      try {
        const cSwal = await import('@/utils/cSwal.js');
        const result = await cSwal.default.cConfirm(
          'Deseja aprovar todos os itens do orçamento?',
          'Aprovar Todos os Itens'
        );

        if (result.isConfirmed) {
          // Fazer requisição para o backend
          const { orcamentoService } = await import('@/services/orcamentoService');
          const response = await orcamentoService.aprovarTodosItens(
            this.orcamentoSelecionado.id
          );

          // Atualizar orçamento com dados do backend
          this.orcamentoSelecionado = response.data.data;

          cSwal.default.cSuccess('Todos os itens foram aprovados!');
        }
      } catch (error) {
        console.error('Erro ao aprovar todos os itens:', error);
        const cSwal = await import('@/utils/cSwal.js');
        cSwal.default.cError('Erro ao aprovar todos os itens');
      }
    },

    async rejeitarTodosItens() {
      try {
        const cSwal = await import('@/utils/cSwal.js');
        const { value: motivo } = await cSwal.default.cInput(
          'Motivo da rejeição em massa (opcional):',
          'Rejeitar Todos os Itens'
        );

        if (motivo !== undefined) { // undefined = cancelou
          // Fazer requisição para o backend
          const { orcamentoService } = await import('@/services/orcamentoService');
          const response = await orcamentoService.rejeitarTodosItens(
            this.orcamentoSelecionado.id,
            motivo || 'Rejeitado em massa'
          );

          // Atualizar orçamento com dados do backend
          this.orcamentoSelecionado = response.data.data;

          cSwal.default.cSuccess('Todos os itens foram rejeitados!');
        }
      } catch (error) {
        console.error('Erro ao rejeitar todos os itens:', error);
        const cSwal = await import('@/utils/cSwal.js');
        cSwal.default.cError('Erro ao rejeitar todos os itens');
      }
    },

    // ========== MÉTODOS DE EDIÇÃO NO MODO VISUALIZAÇÃO ==========
    editOrcamentoItemInView(item) {
      // Encontrar o índice real do item no orçamento
      const realIndex = this.orcamentoSelecionado.itens.findIndex(i => i === item);

      // Carregar dados do item no formulário
      this.itemForm = { ...item };
      this.editingItemIndex = realIndex;
      this.showItemEditor = true;
    },

    async removeOrcamentoItemInView(item) {
      try {
        const cSwal = await import('@/utils/cSwal.js');
        const result = await cSwal.default.cConfirm(
          'Deseja remover este item do orçamento?',
          'Remover Item'
        );

        if (result.isConfirmed) {
          // Fazer requisição para o backend
          const { orcamentoService } = await import('@/services/orcamentoService');
          const response = await orcamentoService.removerItem(
            this.orcamentoSelecionado.id,
            item.id
          );

          // Atualizar orçamento com dados do backend
          this.orcamentoSelecionado = response.data.data;

          cSwal.default.cSuccess('Item removido com sucesso!');
        }
      } catch (error) {
        console.error('Erro ao remover item:', error);
        const cSwal = await import('@/utils/cSwal.js');
        cSwal.default.cError('Erro ao remover item');
      }
    },

    // ========== MÉTODOS DE EDIÇÃO INLINE DO ORÇAMENTO ==========
    startEditingTitle() {
      this.editingTitleValue = this.orcamentoSelecionado?.titulo || '';
      this.editingOrcamentoTitle = true;
      this.$nextTick(() => {
        if (this.$refs.titleInput) {
          this.$refs.titleInput.focus();
          this.$refs.titleInput.select();
        }
      });
    },

    async saveTitle() {
      if (!this.editingTitleValue.trim()) {
        const cSwal = await import('@/utils/cSwal.js');
        cSwal.default.cError('O título não pode estar vazio');
        return;
      }

      try {
        // Fazer requisição para o backend
        const { orcamentoService } = await import('@/services/orcamentoService');
        const response = await orcamentoService.updateOrcamento(this.orcamentoSelecionado.id, {
          titulo: this.editingTitleValue.trim()
        });

        // Atualizar localmente com dados do backend
        this.orcamentoSelecionado.titulo = response.data.data.titulo;
        this.editingOrcamentoTitle = false;

        const cSwal = await import('@/utils/cSwal.js');
        cSwal.default.cSuccess('Título atualizado com sucesso!');
      } catch (error) {
        console.error('Erro ao salvar título:', error);
        const cSwal = await import('@/utils/cSwal.js');
        cSwal.default.cError('Erro ao salvar título');
        // Reverter valor local em caso de erro
        this.editingTitleValue = this.orcamentoSelecionado?.titulo || '';
      }
    },

    cancelEditTitle() {
      this.editingOrcamentoTitle = false;
      this.editingTitleValue = '';
    },

    startEditingDescription() {
      this.editingDescriptionValue = this.orcamentoSelecionado?.descricao || '';
      this.editingOrcamentoDescription = true;
      this.$nextTick(() => {
        if (this.$refs.descriptionInput) {
          this.$refs.descriptionInput.focus();
        }
      });
    },

    async saveDescription() {
      try {
        // Fazer requisição para o backend
        const { orcamentoService } = await import('@/services/orcamentoService');
        const response = await orcamentoService.updateOrcamento(this.orcamentoSelecionado.id, {
          descricao: this.editingDescriptionValue.trim()
        });

        // Atualizar localmente com dados do backend
        this.orcamentoSelecionado.descricao = response.data.data.descricao;
        this.editingOrcamentoDescription = false;

        const cSwal = await import('@/utils/cSwal.js');
        cSwal.default.cSuccess('Descrição atualizada com sucesso!');
      } catch (error) {
        console.error('Erro ao salvar descrição:', error);
        const cSwal = await import('@/utils/cSwal.js');
        cSwal.default.cError('Erro ao salvar descrição');
        // Reverter valor local em caso de erro
        this.editingDescriptionValue = this.orcamentoSelecionado?.descricao || '';
      }
    },

    cancelEditDescription() {
      this.editingOrcamentoDescription = false;
      this.editingDescriptionValue = '';
    },

    startEditingDate() {
      // Converter data para formato YYYY-MM-DD para o input date
      const dataOrcamento = this.orcamentoSelecionado?.data_orcamento;
      if (dataOrcamento) {
        try {
          // Criar objeto Date e extrair apenas a parte da data (YYYY-MM-DD)
          const date = new Date(dataOrcamento);
          if (!isNaN(date.getTime())) {
            // Formatar para YYYY-MM-DD (formato esperado pelo input date)
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            this.editingDateValue = `${year}-${month}-${day}`;
          } else {
            this.editingDateValue = '';
          }
        } catch (error) {
          console.error('Erro ao converter data:', error);
          this.editingDateValue = '';
        }
      } else {
        this.editingDateValue = '';
      }

      this.editingOrcamentoDate = true;
      this.$nextTick(() => {
        if (this.$refs.dateInput) {
          this.$refs.dateInput.focus();
        }
      });
    },

    async saveDate() {
      if (!this.editingDateValue) {
        const cSwal = await import('@/utils/cSwal.js');
        cSwal.default.cError('A data não pode estar vazia');
        return;
      }

      try {
        // Fazer requisição para o backend
        const { orcamentoService } = await import('@/services/orcamentoService');
        const response = await orcamentoService.updateOrcamento(this.orcamentoSelecionado.id, {
          data_orcamento: this.editingDateValue
        });

        // Atualizar localmente com dados do backend
        this.orcamentoSelecionado.data_orcamento = response.data.data.data_orcamento;
        this.editingOrcamentoDate = false;

        const cSwal = await import('@/utils/cSwal.js');
        cSwal.default.cSuccess('Data atualizada com sucesso!');
      } catch (error) {
        console.error('Erro ao salvar data:', error);
        const cSwal = await import('@/utils/cSwal.js');
        cSwal.default.cError('Erro ao salvar data');
        // Reverter valor local em caso de erro
        this.startEditingDate(); // Recarregar valor original
      }
    },

    cancelEditDate() {
      this.editingOrcamentoDate = false;
      this.editingDateValue = '';
    },

    // ========== MÉTODOS DE ALTERAÇÃO DE STATUS ==========
    async enviarOrcamento(orcamento) {
      this.activeDropdown = null; // Fechar dropdown
      try {
        const cSwal = await import('@/utils/cSwal.js');
        const result = await cSwal.default.cConfirm(
          'Deseja enviar este orçamento para aprovação?',
          'Enviar Orçamento'
        );

        if (result.isConfirmed) {
          const { orcamentoService } = await import('@/services/orcamentoService');
          const response = await orcamentoService.enviarOrcamento(orcamento.id);

          // Atualizar o orçamento na lista
          this.$emit('orcamento-updated', response.data.data);
          cSwal.default.cSuccess('Orçamento enviado com sucesso!');
        }
      } catch (error) {
        console.error('Erro ao enviar orçamento:', error);
        const cSwal = await import('@/utils/cSwal.js');
        cSwal.default.cError('Erro ao enviar orçamento: ' + (error.response?.data?.message || error.message));
      }
    },

    async aprovarOrcamento(orcamento) {
      this.activeDropdown = null; // Fechar dropdown
      try {
        const cSwal = await import('@/utils/cSwal.js');
        const result = await cSwal.default.cConfirm(
          'Deseja aprovar este orçamento?',
          'Aprovar Orçamento'
        );

        if (result.isConfirmed) {
          const { orcamentoService } = await import('@/services/orcamentoService');
          const response = await orcamentoService.aprovarOrcamento(orcamento.id);

          // Atualizar o orçamento na lista
          this.$emit('orcamento-updated', response.data.data);
          cSwal.default.cSuccess('Orçamento aprovado com sucesso!');
        }
      } catch (error) {
        console.error('Erro ao aprovar orçamento:', error);
        const cSwal = await import('@/utils/cSwal.js');
        cSwal.default.cError('Erro ao aprovar orçamento: ' + (error.response?.data?.message || error.message));
      }
    },

    async rejeitarOrcamento(orcamento) {
      this.activeDropdown = null; // Fechar dropdown
      try {
        const cSwal = await import('@/utils/cSwal.js');
        const result = await cSwal.default.cConfirm(
          'Deseja recusar este orçamento?',
          'Recusar Orçamento',
          'warning'
        );

        if (result.isConfirmed) {
          const { orcamentoService } = await import('@/services/orcamentoService');
          const response = await orcamentoService.rejeitarOrcamento(orcamento.id);

          // Atualizar o orçamento na lista
          this.$emit('orcamento-updated', response.data.data);
          cSwal.default.cSuccess('Orçamento recusado com sucesso!');
        }
      } catch (error) {
        console.error('Erro ao recusar orçamento:', error);
        const cSwal = await import('@/utils/cSwal.js');
        cSwal.default.cError('Erro ao recusar orçamento: ' + (error.response?.data?.message || error.message));
      }
    },

    async duplicarOrcamento(orcamento) {
      this.activeDropdown = null; // Fechar dropdown
      try {
        const cSwal = await import('@/utils/cSwal.js');
        const result = await cSwal.default.cConfirm(
          'Deseja duplicar este orçamento?',
          'Duplicar Orçamento'
        );

        if (result.isConfirmed) {
          // Emitir evento para o componente pai duplicar o orçamento
          this.$emit('duplicate-orcamento', orcamento);
        }
      } catch (error) {
        console.error('Erro ao duplicar orçamento:', error);
        const cSwal = await import('@/utils/cSwal.js');
        cSwal.default.cError('Erro ao duplicar orçamento');
      }
    },

    async excluirOrcamento(orcamento) {
      this.activeDropdown = null; // Fechar dropdown
      try {
        const cSwal = await import('@/utils/cSwal.js');
        const result = await cSwal.default.cConfirm(
          'Tem certeza que deseja excluir este orçamento? Esta ação não pode ser desfeita.',
          'Excluir Orçamento',
          'error'
        );

        if (result.isConfirmed) {
          // Emitir evento para o componente pai excluir o orçamento
          this.$emit('delete-orcamento', orcamento.id);
        }
      } catch (error) {
        console.error('Erro ao excluir orçamento:', error);
        const cSwal = await import('@/utils/cSwal.js');
        cSwal.default.cError('Erro ao excluir orçamento');
      }
    },

    async enviarPorEmail(orcamento) {
      this.activeDropdown = null; // Fechar dropdown
      try {
        const cSwal = await import('@/utils/cSwal.js');
        const result = await cSwal.default.cConfirm(
          'Deseja enviar este orçamento por e-mail?',
          'Enviar por E-mail'
        );

        if (result.isConfirmed) {
          // Emitir evento para o componente pai enviar por email
          this.$emit('send-email-orcamento', orcamento);
        }
      } catch (error) {
        console.error('Erro ao enviar orçamento por email:', error);
        const cSwal = await import('@/utils/cSwal.js');
        cSwal.default.cError('Erro ao enviar orçamento por e-mail');
      }
    },

    // ========== MÉTODOS DE NAVEGAÇÃO DE VIEWS ==========
    async viewOrcamento(orcamento) {
      try {
        // Carregar dados frescos do backend
        const { orcamentoService } = await import('@/services/orcamentoService');
        const response = await orcamentoService.getOrcamento(orcamento.id);
        const orcamentoFresco = response.data.data;

        this.orcamentoSelecionado = orcamentoFresco;
        this.viewMode = 'viewing-orcamento';
        this.orcamentoIniciado = true; // Marcar como iniciado pois está visualizando um orçamento existente
      } catch (error) {
        console.error('Erro ao carregar dados do orçamento:', error);
        const cSwal = await import('@/utils/cSwal.js');
        cSwal.default.cError('Erro ao carregar dados do orçamento');
      }
    },

    startCreatingOrcamento() {
      // Apenas mudar para modo de criação (sem gravar no banco)
      this.viewMode = 'creating-orcamento';
      this.editingOrcamento = false;
      this.orcamentoIniciado = false; // Resetar flag ao criar novo orçamento
      this.resetOrcamentoForm();

      // Dar focus no input do título
      this.$nextTick(() => {
        if (this.$refs.tituloOrcamentoInput && window.innerWidth >= 768) {
          this.$refs.tituloOrcamentoInput.focus();
        }
      });
    },

    startCreatingFatura() {
      this.viewMode = 'creating-fatura';
      this.resetFaturaForm();
    },

    async voltarParaLista() {
      // Emitir evento para o componente pai recarregar a lista de orçamentos
      this.$emit('reload-orcamentos');

      // Voltar para lista normal
      this.viewMode = 'normal';
      this.orcamentoSelecionado = null;
      this.editingOrcamento = false;
      this.orcamentoIniciado = false;
      this.resetOrcamentoForm();
    },

    cancelCreation() {
      // Se estava editando um orçamento, voltar para visualização
      if (this.editingOrcamento && this.orcamentoSelecionado) {
        this.viewMode = 'viewing-orcamento';
        this.editingOrcamento = false;
        this.orcamentoIniciado = true; // Manter como iniciado se estava editando
        this.resetOrcamentoForm();
      } else {
        // Caso contrário, voltar para lista normal
        this.viewMode = 'normal';
        this.orcamentoSelecionado = null;
        this.editingOrcamento = false;
        this.orcamentoIniciado = false;
        this.resetOrcamentoForm();
        this.resetFaturaForm();
      }
    },

    resetOrcamentoForm() {
      // Obter dentista_id padrão
      let dentistaIdPadrao = '';
      if (this.$dentista && this.$dentista.id) {
        dentistaIdPadrao = this.$dentista.id;
      }

      this.novoOrcamentoForm = {
        paciente_id: this.paciente?.id || '',
        dentista_id: dentistaIdPadrao,
        titulo: '',
        descricao: '',
        data_validade: '',
        observacoes: '',
        itens: []
      };
      this.orcamentoErrors = {};
    },

    resetFaturaForm() {
      // Obter dentista_id padrão
      let dentistaIdPadrao = '';
      if (this.$dentista && this.$dentista.id) {
        dentistaIdPadrao = this.$dentista.id;
      }

      this.novaFaturaForm = {
        paciente_id: this.paciente?.id || '',
        dentista_id: dentistaIdPadrao,
        descricao: '',
        valor_nominal: '',
        data_vencimento: '',
        parcelas_total: 1,
        percentual_desconto: '',
        valor_desconto: '',
        percentual_acrescimo: '',
        valor_acrescimo: '',
        observacoes: ''
      };
      this.faturaErrors = {};
    },



    isStatusActive(status) {
      return this.filters.statusList.includes(status);
    },

    toggleStatus(status) {
      const index = this.filters.statusList.indexOf(status);
      if (index > -1) {
        // Remove o status se já estiver selecionado
        this.filters.statusList.splice(index, 1);
      } else {
        // Adiciona o status se não estiver selecionado
        this.filters.statusList.push(status);
      }
    },

    markAsPaid(fatura) {
      const paymentData = {
        data_pagamento: new Date().toISOString().split('T')[0],
        meio_pagamento: 'Não especificado'
      };
      this.$emit('mark-paid', fatura.id, paymentData);
    },

    isOverdue(fatura) {
      if (fatura.status !== 'pendente') return false;
      return new Date(fatura.data_vencimento) < new Date();
    },

    getDaysOverdue(fatura) {
      if (!this.isOverdue(fatura)) return 0;
      const today = new Date();
      const vencimento = new Date(fatura.data_vencimento);
      const diffTime = today - vencimento;
      return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    },

    // ========== MÉTODOS PARA ORÇAMENTO ==========
    addOrcamentoItem() {
      this.showItemEditor = true;
      this.editingItemIndex = null;
      this.resetItemForm();
      this.clearItemErrors();
    },

    editOrcamentoItem(index) {
      this.showItemEditor = true;
      this.editingItemIndex = index;
      const item = this.novoOrcamentoForm.itens[index];
      this.itemForm = { ...item };
      this.clearItemErrors();
    },

    closeItemEditor() {
      this.showItemEditor = false;
      this.editingItemIndex = null;
      this.resetItemForm();
      this.clearItemErrors();
    },

    resetItemForm() {
      this.itemForm = {
        servico_produto_id: null,
        nome: '',
        descricao: '',
        quantidade: 1,
        valor_unitario: 0,
        desconto_percentual: 0,
        desconto_valor: 0,
        observacoes: '',
        dentes: []
        // Não incluir 'id' aqui para novos itens
      };
    },

    clearItemErrors() {
      this.itemErrors = {};
    },

    updateQuantidadeFromDentes(dentes) {
      // Atualizar quantidade baseado no número de dentes selecionados
      if (dentes && dentes.length > 0) {
        this.itemForm.quantidade = dentes.length;
      } else {
        this.itemForm.quantidade = 1;
      }
    },

    onServicoChange() {
      if (!this.itemForm.servico_produto_id) return;

      const servico = this.servicosProdutos.find(s => s.id == this.itemForm.servico_produto_id);
      if (servico) {
        this.itemForm.nome = servico.nome || '';
        this.itemForm.valor_unitario = servico.valor_base || 0;
        this.itemForm.descricao = servico.descricao || '';
      }
    },

    validateItemForm() {
      this.itemErrors = {};

      if (!this.itemForm.servico_produto_id) {
        this.itemErrors.servico_produto_id = 'Selecione um serviço/procedimento';
      }

      if (!this.itemForm.quantidade || this.itemForm.quantidade <= 0) {
        this.itemErrors.quantidade = 'Quantidade deve ser maior que zero';
      }

      if (!this.itemForm.valor_unitario || this.itemForm.valor_unitario <= 0) {
        this.itemErrors.valor_unitario = 'Valor unitário deve ser maior que zero';
      }

      return Object.keys(this.itemErrors).length === 0;
    },

    async saveItem() {
      if (!this.validateItemForm()) {
        return;
      }

      this.savingItem = true;

      try {
        // Se estamos no modo de visualização (novo orçamento ou edição)
        if (this.viewMode === 'viewing-orcamento' && this.orcamentoSelecionado) {
          const { orcamentoService } = await import('@/services/orcamentoService');

          if (this.editingItemIndex !== null) {
            // Editar item existente - salvar no backend
            const item = this.orcamentoSelecionado.itens[this.editingItemIndex];
            const itemData = { ...this.itemForm };

            const response = await orcamentoService.updateItem(
              this.orcamentoSelecionado.id,
              item.id,
              itemData
            );

            // Atualizar orçamento com dados do backend
            this.orcamentoSelecionado = response.data.data;
          } else {
            // Adicionar novo item - salvar no backend
            const itemData = { ...this.itemForm };
            delete itemData.id; // Garantir que novos itens não tenham ID

            const response = await orcamentoService.addItem(
              this.orcamentoSelecionado.id,
              itemData
            );

            // Atualizar orçamento com dados do backend
            this.orcamentoSelecionado = response.data.data;
          }

          // Emitir evento para o componente pai recarregar a lista de orçamentos
          this.$emit('orcamento-updated', this.orcamentoSelecionado);

          const cSwal = await import('@/utils/cSwal.js');
          cSwal.default.cSuccess('Item salvo com sucesso!');
        } else {
          // Modo de criação antigo (para compatibilidade)
          if (this.editingItemIndex !== null) {
            // Editar item existente
            const updatedItens = [...this.novoOrcamentoForm.itens];
            updatedItens[this.editingItemIndex] = { ...this.itemForm };
            this.novoOrcamentoForm.itens = updatedItens;
          } else {
            // Adicionar novo item (sem ID, pois será criado no backend)
            const newItem = { ...this.itemForm };
            delete newItem.id; // Garantir que novos itens não tenham ID

            this.novoOrcamentoForm.itens.push(newItem);
          }
        }

        this.savingItem = false;
        this.closeItemEditor();
      } catch (error) {
        console.error('Erro ao salvar item:', error);
        const cSwal = await import('@/utils/cSwal.js');
        cSwal.default.cError('Erro ao salvar item: ' + (error.response?.data?.message || error.message));
        this.savingItem = false;
      }
    },

    calculateItemTotal(item) {
      if (!item) return 0;
      const valorBruto = (item.quantidade || 0) * (item.valor_unitario || 0);
      const desconto = (item.desconto_percentual || 0) > 0
        ? valorBruto * (item.desconto_percentual / 100)
        : (item.desconto_valor || 0);
      return Math.max(0, valorBruto - desconto);
    },

    async removeOrcamentoItem(index) {
      if (confirm('Deseja realmente remover este item?')) {
        // Se estamos no modo de criação antigo, remover localmente
        if (this.viewMode === 'creating-orcamento') {
          this.novoOrcamentoForm.itens.splice(index, 1);
        }
      }
    },

    async saveOrcamento() {
      // Validação básica
      if (!this.novoOrcamentoForm.titulo) {
        alert('Por favor, preencha o título do orçamento');
        return;
      }
      if (this.novoOrcamentoForm.itens.length === 0) {
        alert('Adicione pelo menos um item ao orçamento');
        return;
      }

      // Limpar campos vazios antes de enviar
      const orcamentoData = { ...this.novoOrcamentoForm };
      Object.keys(orcamentoData).forEach(key => {
        if (orcamentoData[key] === '' || orcamentoData[key] === null) {
          delete orcamentoData[key];
        }
      });

      // Limpar campos vazios dos itens também
      orcamentoData.itens = orcamentoData.itens.map(item => {
        const cleanItem = { ...item };
        Object.keys(cleanItem).forEach(key => {
          if (cleanItem[key] === '' || cleanItem[key] === null) {
            delete cleanItem[key];
          }
        });

        // Se o item não tem um ID válido (novo item), remover o campo id
        // para que o backend trate como criação de novo item
        if (!cleanItem.id || cleanItem.id <= 0) {
          delete cleanItem.id;
        }

        return cleanItem;
      });

      // Salvar ou atualizar orçamento diretamente
      this.saving = true;



      try {
        let response;
        if (this.editingOrcamento && orcamentoData.id) {
          // Atualizar orçamento existente
          const { orcamentoService } = await import('@/services/orcamentoService');
          response = await orcamentoService.updateOrcamento(orcamentoData.id, orcamentoData);
        } else {
          // Criar novo orçamento
          this.$emit('save-orcamento', orcamentoData);
          return; // Sair aqui para deixar o componente pai lidar com a criação
        }

        // Sucesso na atualização
        const cSwal = await import('@/utils/cSwal.js');
        cSwal.default.cSuccess('Orçamento atualizado com sucesso!');

        // Atualizar a lista de orçamentos
        this.$emit('orcamento-updated', response.data.data);

        // Voltar ao modo de visualização do orçamento (não para a lista)
        this.onOrcamentoUpdated(response.data.data);

      } catch (error) {
        console.error('Erro ao salvar orçamento:', error);
        const cSwal = await import('@/utils/cSwal.js');
        cSwal.default.cError('Erro ao salvar orçamento: ' + (error.response?.data?.message || error.message));
        this.saving = false;
      }
    },

    // Método público para ser chamado pelo componente pai após sucesso
    onOrcamentoSaved() {
      this.saving = false;
      this.cancelCreation();
    },

    // Método para quando orçamento é atualizado (edição)
    onOrcamentoUpdated(orcamentoAtualizado) {
      this.saving = false;
      this.editingOrcamento = false;

      // Atualizar o orçamento selecionado com os dados mais recentes
      this.orcamentoSelecionado = orcamentoAtualizado;

      // Voltar para o modo de visualização do orçamento
      this.viewMode = 'viewing-orcamento';

      // Limpar o formulário de edição
      this.resetOrcamentoForm();
    },

    // Método público para ser chamado pelo componente pai em caso de erro
    onOrcamentoError() {
      this.saving = false;
    },

    // ========== MÉTODOS PARA FATURA ==========
    async saveFatura() {
      // Validação básica
      if (!this.novaFaturaForm.descricao) {
        alert('Por favor, preencha a descrição da fatura');
        return;
      }
      if (!this.novaFaturaForm.valor_nominal) {
        alert('Por favor, preencha o valor nominal');
        return;
      }
      if (!this.novaFaturaForm.data_vencimento) {
        alert('Por favor, preencha a data de vencimento');
        return;
      }

      // Limpar campos vazios antes de enviar
      const faturaData = { ...this.novaFaturaForm };

      // ADICIONAR orcamento_id se estiver gerando fatura a partir de um orçamento
      if (this.orcamentoSelecionado?.id) {
        faturaData.orcamento_id = this.orcamentoSelecionado.id;
      }

      Object.keys(faturaData).forEach(key => {
        if (faturaData[key] === '' || faturaData[key] === null) {
          delete faturaData[key];
        }
      });

      // Emitir evento para o componente pai salvar
      this.saving = true;
      this.$emit('save-fatura', faturaData);
    },

    // Método público para ser chamado pelo componente pai após sucesso
    onFaturaSaved() {
      this.saving = false;
      this.cancelCreation();
    },

    // Método público para ser chamado pelo componente pai em caso de erro
    onFaturaError() {
      this.saving = false;
    },

    // ========== MÉTODOS AUXILIARES ==========
    updateFaturaForm(field, value) {
      this.novaFaturaForm[field] = value;
    },

    // Métodos de formatação de moeda
    formatCurrencyInput(value) {
      if (!value || value === 0) return '';
      return this.formatCurrency(value).replace('R$ ', '');
    },

    // Atualizar Valor Nominal
    updateValorNominal(value) {
      // Remove tudo exceto números e vírgula/ponto
      const cleanValue = value.replace(/[^\d,]/g, '').replace(',', '.');
      this.novaFaturaForm.valor_nominal = parseFloat(cleanValue) || '';
    },

    // Atualizar Valor Desconto
    updateValorDesconto(value) {
      const cleanValue = value.replace(/[^\d,]/g, '').replace(',', '.');
      this.novaFaturaForm.valor_desconto = parseFloat(cleanValue) || '';
      // Limpar percentual quando digitar valor
      if (this.novaFaturaForm.valor_desconto) {
        this.novaFaturaForm.percentual_desconto = '';
      }
    },

    // Atualizar Valor Acréscimo
    updateValorAcrescimo(value) {
      const cleanValue = value.replace(/[^\d,]/g, '').replace(',', '.');
      this.novaFaturaForm.valor_acrescimo = parseFloat(cleanValue) || '';
      // Limpar percentual quando digitar valor
      if (this.novaFaturaForm.valor_acrescimo) {
        this.novaFaturaForm.percentual_acrescimo = '';
      }
    },

    // Limpar valor quando digitar percentual
    clearDescontoValue() {
      if (this.novaFaturaForm.percentual_desconto) {
        this.novaFaturaForm.valor_desconto = '';
      }
    },

    clearAcrescimoValue() {
      if (this.novaFaturaForm.percentual_acrescimo) {
        this.novaFaturaForm.valor_acrescimo = '';
      }
    },

    async loadDentistas() {
      try {
        const response = await getDentistas();
        this.dentistas = response || [];
      } catch (error) {
        console.error('Erro ao carregar dentistas:', error);
        this.dentistas = [];
      }
    },

    async loadServicosProdutos() {
      try {
        const response = await servicoProdutoService.getServicosProdutos({ ativo: 1 });

        console.log('🔍 DEBUG - Resposta completa:', response);
        console.log('🔍 DEBUG - response.data:', response.data);
        console.log('🔍 DEBUG - response.data.data:', response.data?.data);
        console.log('🔍 DEBUG - response.data.data.data:', response.data?.data?.data);

        // Tentar diferentes estruturas de resposta
        let servicos = [];

        // Estrutura 1: { data: { data: { data: [...] } } } (paginação)
        if (response?.data?.data?.data && Array.isArray(response.data.data.data)) {
          servicos = response.data.data.data;
          console.log('✅ Estrutura 1 detectada: response.data.data.data (paginação)');
        }
        // Estrutura 2: { data: { data: [...] } }
        else if (response?.data?.data && Array.isArray(response.data.data)) {
          servicos = response.data.data;
          console.log('✅ Estrutura 2 detectada: response.data.data');
        }
        // Estrutura 3: { data: [...] }
        else if (response?.data && Array.isArray(response.data)) {
          servicos = response.data;
          console.log('✅ Estrutura 3 detectada: response.data');
        }
        // Estrutura 4: [...]
        else if (Array.isArray(response)) {
          servicos = response;
          console.log('✅ Estrutura 4 detectada: response direto');
        }

        this.servicosProdutos = servicos;
        console.log('✅ Total de serviços carregados:', this.servicosProdutos.length);
        console.log('✅ Serviços:', this.servicosProdutos);

        if (this.servicosProdutos.length === 0) {
          console.warn('⚠️ ATENÇÃO: Nenhum serviço foi carregado!');
          console.warn('⚠️ Estrutura da resposta:', JSON.stringify(response, null, 2));
        }
      } catch (error) {
        console.error('❌ Erro ao carregar serviços/produtos:', error);
        this.servicosProdutos = [];
      }
    }
  },

  mounted() {
    console.log('🚀 PacienteFinanceiro mounted - Carregando dados...');
    this.loadDentistas();
    this.loadServicosProdutos();

    // Fechar dropdown ao clicar fora
    document.addEventListener('click', () => {
      this.activeDropdown = null;
    });
  },

  beforeUnmount() {
    // Remover listener
    document.removeEventListener('click', () => {
      this.activeDropdown = null;
    });
  }
}
</script>

<style scoped>
.paciente-financeiro {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.paciente-financeiro .card {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  flex: 1;
  display: flex;
  flex-direction: column;
}

.paciente-financeiro .card-body {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* ========== LAYOUT DE DUAS COLUNAS ========== */
.row.g-0.h-100 {
  min-height: 600px;
}

.orcamentos-section,
.faturas-section {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.section-header {
  padding: 0.5rem 1rem;
  border-bottom: 1px solid #e9ecef;
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.section-content {
  flex: 1;
  overflow-y: auto;
  padding: 0.5rem;
}

/* Animação aplicada aos conteúdos dinâmicos */
.section-content > div,
.orcamento-itens-section > div,
.fatura-campos-section > div {
  animation: fadeIn 0.4s ease-in-out;
}

/* ========== ANIMAÇÕES DE TRANSIÇÃO ========== */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.filters-section {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #e9ecef;
  background: #f8f9fa;
  flex-shrink: 0;
}

.stats-section {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #e9ecef;
  background: #fff;
  flex-shrink: 0;
}

.faturas-table-section {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

.faturas-table-section .table-responsive {
  height: 100%;
}

/* Estilos para os cards de estatísticas */
.stats-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 0.8rem;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
}

.stats-title {
  font-size: 0.75rem;
  font-weight: 600;
  color: #344767;
  text-transform: uppercase;
  margin-bottom: 0.4rem;
  letter-spacing: 0.5px;
}

.stats-value {
  font-size: 1.1rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
}

.badge-warning {
  background-color: #fb6340;
}

.badge-success {
  background-color: #2dce89;
}

.badge-danger {
  background-color: #f5365c;
}

.badge-secondary {
  background-color: #8898aa;
}

.text-info {
  color: #11cdef !important;
}

/* Botões de Ação Elegantes */
.elegant-action-btn {
  min-width: 140px;
  min-height: 80px;
  border-radius: 12px;
  transition: all 0.3s ease;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.elegant-action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.elegant-action-btn i {
  transition: all 0.3s ease;
}

.elegant-action-btn:hover i {
  transform: scale(1.1);
}

/* Botões de Ação Pequenos */
.elegant-action-btn-small {
  min-width: 120px;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  transition: all 0.3s ease;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.elegant-action-btn-small:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Estilos para os toggles de status */
.status-toggles {
  display: flex;
  gap: 0;
  border-radius: 0.375rem;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  width: 100%;
}

.status-toggle {
  border-radius: 0 !important;
  border-right: none !important;
  font-size: 0.75rem;
  padding: 0.4rem 0.5rem;
  font-weight: 600;
  transition: all 0.2s ease;
  white-space: nowrap;
  flex: 1;
  text-align: center;
}

.status-toggle:last-child {
  border-right: 1px solid !important;
}

.status-toggle:first-child {
  border-top-left-radius: 0.375rem !important;
  border-bottom-left-radius: 0.375rem !important;
}

.status-toggle:last-child {
  border-top-right-radius: 0.375rem !important;
  border-bottom-right-radius: 0.375rem !important;
}

.status-toggle:hover {
  transform: translateY(-1px);
  z-index: 2;
  position: relative;
}

/* Botões de Ação Verticais */
.elegant-action-btn-vertical {
  min-height: 80px;
  border-radius: 8px;
  transition: all 0.3s ease;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 0.75rem 0.5rem;
}

.elegant-action-btn-vertical:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.elegant-action-btn-vertical i {
  transition: all 0.3s ease;
}

.elegant-action-btn-vertical:hover i {
  transform: scale(1.1);
}

.elegant-action-btn-vertical div {
  font-size: 0.85rem;
  line-height: 1.2;
}

/* ========== ESTILOS PARA ORÇAMENTOS EM LISTA ========== */
.orcamentos-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding: 0.5rem;
}

.orcamento-item {
  background: #fff;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 0.75rem;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
}

.orcamento-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-color: #007bff;
}

.orcamento-item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
}

.orcamento-item-title h6 {
  color: #344767;
  font-weight: 600;
  margin-bottom: 0.25rem;
  font-size: 0.85rem;
}

.orcamento-item-title small {
  color: #8898aa;
  font-size: 0.7rem;
}

.orcamento-item-body {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
}

.orcamento-item-info {
  flex: 1;
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 0.25rem;
  font-size: 0.75rem;
  color: #8898aa;
}

.info-row .text-sm {
  font-size: 0.8rem;
  color: #495057;
  font-weight: 500;
}

.orcamento-item-valor {
  text-align: right;
}

.valor-principal {
  font-size: 0.9rem;
  font-weight: 700;
  color: #2dce89;
}

.valor-original {
  display: block;
  font-size: 0.7rem;
  text-decoration: line-through;
  margin-top: 0.25rem;
}

.orcamento-item-actions {
  display: flex;
  gap: 0.25rem;
  align-items: center;
  justify-content: flex-end;
}

.orcamento-item-actions .btn {
  padding: 0.2rem 0.4rem;
  font-size: 0.7rem;
}

/* ========== CARDS DE ESTATÍSTICAS MINI ========== */
.stats-card-mini {
  background: #fff;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 0.5rem;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  height: 55px;
}

.stats-card-mini:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.stats-card-mini .stats-icon {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.5rem;
  font-size: 0.9rem;
  background: rgba(0, 0, 0, 0.05);
}

.stats-card-mini .stats-content {
  flex: 1;
}

.stats-card-mini .stats-title {
  font-size: 0.65rem;
  font-weight: 600;
  color: #8898aa;
  text-transform: uppercase;
  margin-bottom: 0.2rem;
  letter-spacing: 0.3px;
}

.stats-card-mini .stats-value {
  font-size: 0.8rem;
  font-weight: 700;
}

/* Manter compatibilidade com cards compactos existentes */
.stats-card-compact {
  background: #fff;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 0.75rem;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  height: 70px;
}

.stats-card-compact:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
}

.stats-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.75rem;
  font-size: 1.1rem;
  background: rgba(0, 0, 0, 0.05);
}

.stats-content {
  flex: 1;
}

.stats-card-compact .stats-title {
  font-size: 0.7rem;
  font-weight: 600;
  color: #8898aa;
  text-transform: uppercase;
  margin-bottom: 0.25rem;
  letter-spacing: 0.5px;
}

.stats-card-compact .stats-value {
  font-size: 0.95rem;
  font-weight: 700;
}

/* ========== CARDS TOGGLE DE STATUS ========== */
.stats-card-toggle {
  background: #fff;
  border: 2px solid #e9ecef;
  border-radius: 10px;
  padding: 0.6rem;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  height: 65px;
  cursor: pointer;
  position: relative;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
}

.stats-card-toggle:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
}

.stats-card-toggle.disabled {
  border-color: #e9ecef;
  background: #f8f9fa;
  opacity: 0.6;
}

.stats-card-toggle .stats-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.6rem;
  font-size: 1rem;
  background: rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.stats-card-toggle .stats-content {
  flex: 1;
}

.stats-card-toggle .stats-title {
  font-size: 0.68rem;
  font-weight: 600;
  color: #8898aa;
  text-transform: uppercase;
  margin-bottom: 0.25rem;
  letter-spacing: 0.3px;
  transition: all 0.3s ease;
}

.stats-card-toggle .stats-value {
  font-size: 0.85rem;
  font-weight: 700;
  transition: all 0.3s ease;
}

.stats-card-toggle .toggle-checkbox {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 18px;
  height: 18px;
  border: 2px solid #dee2e6;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  transition: all 0.3s ease;
  font-size: 10px;
}

.stats-card-toggle.disabled .stats-title,
.stats-card-toggle.disabled .stats-value {
  color: #8898aa !important;
}

.stats-card-toggle.disabled .stats-icon {
  background: rgba(0, 0, 0, 0.03);
}

/* Estilos específicos para cada status quando ativo */
.stats-card-pago.active {
  border-color: rgba(40, 167, 69, 0.4);
  background: rgba(40, 167, 69, 0.02);
}

.stats-card-pago.active .toggle-checkbox {
  border-color: #28a745;
  background: #28a745;
  color: white;
}

.stats-card-pendente.active {
  border-color: rgba(255, 193, 7, 0.4);
  background: rgba(255, 193, 7, 0.02);
}

.stats-card-pendente.active .toggle-checkbox {
  border-color: #ffc107;
  background: #ffc107;
  color: white;
}

.stats-card-vencido.active {
  border-color: rgba(220, 53, 69, 0.4);
  background: rgba(220, 53, 69, 0.02);
}

.stats-card-vencido.active .toggle-checkbox {
  border-color: #dc3545;
  background: #dc3545;
  color: white;
}

.stats-card-cancelado.active {
  border-color: rgba(108, 117, 125, 0.4);
  background: rgba(108, 117, 125, 0.02);
}

.stats-card-cancelado.active .toggle-checkbox {
  border-color: #6c757d;
  background: #6c757d;
  color: white;
}

/* ========== HEADER GRADIENTES ========== */
.bg-gradient-primary {
  background: linear-gradient(87deg, #5e72e4 0, #825ee4 100%);
}

.bg-gradient-blue {
  background: linear-gradient(87deg, #11cdef 0, #1171ef 100%);
}

.bg-gradient-blue-subtle {
  background: linear-gradient(135deg, rgba(17, 205, 239, 0.08) 0%, rgba(17, 113, 239, 0.12) 100%);
  border-bottom: 1px solid rgba(17, 205, 239, 0.15);
}

/* ========== CAMPO DE BUSCA COM ÍCONE ========== */
.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-input-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
  font-size: 14px;
  z-index: 2;
  transition: color 0.3s ease;
}

.search-input-with-icon {
  padding-left: 36px !important;
  transition: all 0.3s ease;
}

.search-input-with-icon:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1);
}

.search-input-with-icon:focus + .search-input-icon,
.search-input-wrapper:focus-within .search-input-icon {
  color: #007bff;
}

/* ========== ESTILOS PARA MODO DE CRIAÇÃO ========== */
.orcamento-resumo-card {
  animation: slideInLeft 0.4s ease-in-out;
}

.fatura-resumo-card {
  animation: slideInLeft 0.4s ease-in-out;
}

.orcamento-itens-section {
  flex: 1;
  overflow-y: auto;
  animation: slideInRight 0.4s ease-in-out;
}

.fatura-campos-section {
  flex: 1;
  overflow-y: auto;
  animation: slideInRight 0.4s ease-in-out;
}

.valores-resumo {
  border: 1px solid #e9ecef;
}

.item-card {
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.item-card:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.empty-state {
  animation: fadeIn 0.5s ease-in-out;
}

.total-section {
  border: 2px solid #e9ecef;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* ========== EDITOR DE ITENS DO ORÇAMENTO ========== */
.item-editor-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  animation: fadeIn 0.3s ease-in-out;
}

.item-editor-panel {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 900px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
  animation: slideInUp 0.3s ease-in-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.item-editor-header {
  padding: 1.25rem 1.5rem;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, rgba(17, 205, 239, 0.08) 0%, rgba(17, 113, 239, 0.12) 100%);
}

.item-editor-header h6 {
  color: #344767;
  font-weight: 600;
}

/* Header compacto do modal */
.item-editor-header-compact {
  padding: 0.85rem 1.2rem;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, rgba(17, 205, 239, 0.08) 0%, rgba(17, 113, 239, 0.12) 100%);
}

.item-editor-header-compact h6 {
  color: #344767;
  font-weight: 600;
  font-size: 0.95rem;
}

.item-editor-body {
  padding: 1.5rem;
  overflow-y: auto;
  flex: 1;
}

.item-editor-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid #e9ecef;
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  background: #f8f9fa;
}

/* Garantir largura fixa das colunas para não "balançar" */
@media (min-width: 992px) {
  .col-lg-4.col-md-5 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
  }

  .col-lg-8.col-md-7 {
    flex: 0 0 66.666667%;
    max-width: 66.666667%;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  .col-lg-4.col-md-5 {
    flex: 0 0 41.666667%;
    max-width: 41.666667%;
  }

  .col-lg-8.col-md-7 {
    flex: 0 0 58.333333%;
    max-width: 58.333333%;
  }
}

/* ========== RESPONSIVIDADE ========== */
@media (max-width: 991px) {
  .row.g-0.h-100 {
    min-height: auto;
  }

  .col-lg-4.col-md-5,
  .col-lg-8.col-md-7 {
    border-bottom: 1px solid #e9ecef;
    min-width: 100%;
    max-width: 100%;
  }

  .col-lg-4.col-md-5 {
    border-right: none;
  }

  .orcamentos-section {
    max-height: 400px;
  }
}

@media (max-width: 768px) {
  .row.g-0.h-100 {
    flex-direction: column;
  }

  .col-lg-4.col-md-5,
  .col-lg-8.col-md-7 {
    max-width: 100%;
    flex: none;
  }

  .orcamentos-section {
    max-height: 300px;
  }

  .section-header {
    padding: 0.4rem 0.75rem;
  }

  .filters-section,
  .stats-section {
    padding: 0.5rem 0.75rem;
  }

  .stats-card-toggle {
    height: 58px;
    padding: 0.45rem;
  }

  .stats-card-toggle .stats-icon {
    width: 28px;
    height: 28px;
    font-size: 0.85rem;
    margin-right: 0.45rem;
  }

  .stats-card-toggle .stats-title {
    font-size: 0.62rem;
  }

  .stats-card-toggle .stats-value {
    font-size: 0.78rem;
  }

  .stats-card-toggle .toggle-checkbox {
    width: 16px;
    height: 16px;
    top: 6px;
    right: 6px;
    font-size: 9px;
  }

  .search-input-icon {
    font-size: 13px;
    left: 10px;
  }

  .search-input-with-icon {
    padding-left: 32px !important;
  }

  .orcamento-item {
    padding: 0.5rem;
  }

  .orcamento-item-title h6 {
    font-size: 0.8rem;
  }

  .stats-card-mini {
    height: 50px;
    padding: 0.4rem;
  }

  .stats-card-mini .stats-icon {
    width: 25px;
    height: 25px;
    font-size: 0.8rem;
    margin-right: 0.4rem;
  }

  .stats-card-mini .stats-title {
    font-size: 0.6rem;
  }

  .stats-card-mini .stats-value {
    font-size: 0.75rem;
  }
}

/* ========== ESTILOS DO FORMULÁRIO ELEGANTE DE FATURA ========== */
/* Seções do Formulário */
.form-section {
  background: white;
  border-radius: 12px;
  padding: 1.25rem;
  border: 1px solid #e9ecef;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
  height: fit-content;
}

.form-section:hover {
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
}

.section-header {
  display: flex;
  align-items: center;
  font-weight: 600;
  color: #495057;
  margin-bottom: 1rem;
  font-size: 0.95rem;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid #f1f3f4;
}

.section-header i {
  color: #2C82C9;
  font-size: 1.1rem;
}

/* Labels Elegantes */
.elegant-label {
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.4rem;
  font-size: 0.8rem;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

/* Inputs Elegantes */
.elegant-input {
  border: 1px solid #dee2e6;
  padding: 0.5rem 0.6rem;
  font-size: 0.9rem;
  min-height: 38px;
  transition: all 0.3s ease;
}

.elegant-input:focus {
  border-color: #2C82C9;
  box-shadow: 0 0 0 0.15rem rgba(44, 130, 201, 0.15);
  background: white;
  outline: none;
}

/* Input Groups Elegantes */
.elegant-input-group {
  position: relative;
  display: flex;
  width: 100%;
  min-height: 38px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border: 1px solid #dee2e6;
  overflow: hidden;
  transition: all 0.3s ease;
}

.elegant-input-group:focus-within {
  border-color: #2C82C9;
  box-shadow: 0 0 0 0.15rem rgba(44, 130, 201, 0.15);
}

.elegant-input-group .elegant-input {
  border: none;
  background: transparent;
  flex: 1;
  padding: 0.5rem 0.6rem;
  font-size: 0.9rem;
  min-height: 36px;
  outline: none;
  transition: all 0.3s ease;
}

.elegant-input-group .elegant-input:focus {
  outline: none;
  background: rgba(44, 130, 201, 0.02);
}

.elegant-addon {
  background: linear-gradient(135deg, #7bb3e0 0%, #6ba3d0 100%);
  color: white;
  font-weight: 600;
  font-size: 0.85rem;
  padding: 0.5rem 0.6rem;
  width: 50px;
  min-height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  flex-shrink: 0;
  border: none;
}

.elegant-addon-left {
  border-right: 1px solid rgba(255, 255, 255, 0.2);
}

.elegant-addon-right {
  border-left: 1px solid rgba(255, 255, 255, 0.2);
}

/* Campos maiores para Valor Nominal e Vencimento */
.large-input {
  min-height: 48px !important;
  padding: 0.7rem 0.8rem;
  font-size: 1rem;
}

.large-input-group {
  min-height: 48px;
}

.large-input-group .elegant-input {
  min-height: 48px;
  padding: 0.7rem 0.8rem;
  font-size: 1rem;
}

.large-input-group .elegant-addon {
  min-height: 48px;
  padding: 0.7rem 0.8rem;
  font-size: 0.9rem;
}

/* Highlight para campo ativo */
.field-active-group {
  border-color: #2C82C9 !important;
  box-shadow: 0 0 0 0.15rem rgba(44, 130, 201, 0.15) !important;
}

/* Inputs de moeda e percentual */
.money-input {
  font-weight: 600;
  color: #28a745;
}

.percent-input {
  font-weight: 600;
  color: #2C82C9;
}

/* ========== ESTILOS ELEGANTES PARA ORÇAMENTO ========== */
.elegant-label-orcamento {
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.5rem;
  font-size: 0.85rem;
  letter-spacing: 0.3px;
}

.elegant-input-orcamento {
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 0.65rem 0.85rem;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  background: #fff;
}

.elegant-input-orcamento:focus {
  border-color: #2C82C9;
  box-shadow: 0 0 0 0.2rem rgba(44, 130, 201, 0.15);
  background: #fff;
  outline: none;
}

.elegant-input-orcamento::placeholder {
  color: #adb5bd;
}

.elegant-select-orcamento {
  padding-top: 0.75rem !important;
  padding-bottom: 0.75rem !important;
  line-height: 1.6;
  height: auto !important;
  min-height: 45px;
}

.bg-gradient-light {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.bg-gradient-green-subtle {
  background: linear-gradient(135deg, rgba(40, 167, 69, 0.08) 0%, rgba(25, 135, 84, 0.12) 100%);
}

.orcamento-resumo-card {
  background: #fff;
  border-radius: 8px;
}

/* Header do orçamento com padding reduzido */
.section-header-orcamento {
  padding: 0.5rem 0.75rem !important;
}

/* Botão de cancelar edição do orçamento */
.btn-cancel-edit-orcamento {
  background: linear-gradient(135deg, #dc3545, #c82333);
  color: white !important;
  border: none;
  border-radius: 6px;
  padding: 0.35rem 0.6rem;
  font-size: 0.7rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.3rem;
  box-shadow: 0 1px 3px rgba(220, 53, 69, 0.2);
  white-space: nowrap;
  line-height: 1;
  letter-spacing: 0.3px;
}

.btn-cancel-edit-orcamento:hover {
  background: linear-gradient(135deg, #c82333, #a71e2a);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
  color: white !important;
}

.btn-cancel-edit-orcamento i {
  font-size: 9px;
  line-height: 1;
  color: white !important;
}

.btn-cancel-edit-orcamento:active {
  transform: translateY(0);
}

/* Container do seletor de dentes no modal */
.dente-selector-container {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 15px;
}

.dente-selector-container .form-label {
  margin-bottom: 0.4rem;
}

/* Inputs compactos e padronizados do modal de item */
.item-input-compact {
  height: 38px;
  font-size: 0.9rem;
  border-radius: 6px;
  border: 1px solid #dee2e6;
  transition: all 0.2s ease;
}

.item-input-compact:focus {
  border-color: #2C82C9;
  box-shadow: 0 0 0 0.15rem rgba(44, 130, 201, 0.15);
}

.item-input-group-compact {
  height: 38px;
}

.item-input-group-compact .input-group-text {
  height: 38px;
  font-size: 0.85rem;
  background: #e9ecef;
  border: 1px solid #dee2e6;
  border-right: none;
}

.item-input-group-compact .form-control {
  height: 38px;
  font-size: 0.9rem;
  border: 1px solid #dee2e6;
  transition: all 0.2s ease;
}

.item-input-group-compact .form-control:focus {
  border-color: #2C82C9;
  box-shadow: 0 0 0 0.15rem rgba(44, 130, 201, 0.15);
}

/* Display do total do item */
.item-total-display {
  height: 38px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 700;
  box-shadow: 0 2px 4px rgba(40, 167, 69, 0.2);
}

/* Estilos para visualização de orçamento */
.orcamento-detalhes-card {
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.orcamento-info {
  padding: 1rem;
}

/* Cards em grid dos itens */
.itens-grid {
  margin-top: 1rem;
}

.item-card-grid {
  transition: all 0.2s ease;
  border: 1px solid #e9ecef !important;
  background: #ffffff !important;
  display: flex;
  flex-direction: column;
}

.item-card-grid:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
  border-color: #007bff !important;
}

/* Card para adicionar novo item (slot fantasma) */
.item-card-add {
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border: 2px dashed #dee2e6 !important;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 200px;
}

.item-card-add:hover {
  border-color: #007bff !important;
  background: linear-gradient(135deg, #e7f3ff 0%, #f8f9fa 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 123, 255, 0.15);
}

.item-card-add.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.item-card-add.disabled:hover {
  border-color: #dee2e6 !important;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  transform: none;
  box-shadow: none;
}

.item-card-add.disabled .add-icon {
  background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
}

.item-card-add.disabled:hover .add-icon {
  transform: none;
  box-shadow: none;
}

.item-card-add .add-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  transition: all 0.3s ease;
}

.item-card-add:hover .add-icon {
  transform: scale(1.1) rotate(90deg);
  box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

.item-card-add .add-text h6 {
  color: #495057;
  font-weight: 600;
  margin: 0;
  transition: color 0.3s ease;
}

.item-card-add:hover .add-text h6 {
  color: #007bff;
}

.item-card-add .add-text p {
  color: #6c757d;
  font-size: 0.875rem;
  margin: 0;
}

/* Botões de ação que aparecem no hover */
.item-card-grid .item-actions {
  opacity: 0;
  transition: opacity 0.2s ease;
}

.item-card-grid:hover .item-actions {
  opacity: 1;
}

/* Valor total do item */
.item-total-value {
  font-size: 1.1rem;
  font-weight: 600;
  padding: 0.5rem;
  border-radius: 0.375rem;
  background: rgba(0, 0, 0, 0.02);
}

.item-card-grid .dentes-badges {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.item-card-grid .dentes-badges .badge-sm {
  font-size: 0.7rem;
  padding: 0.35rem 0.5rem;
  min-width: 32px;
  text-align: center;
  border-radius: 6px;
}

.item-card-grid .item-info {
  flex-grow: 1;
}

.item-card-grid .btn {
  font-size: 0.8rem;
}

/* Cards do modo de edição */
.itens-edit-grid {
  margin-top: 1rem;
}

.item-card-edit {
  transition: all 0.2s ease;
  border: 1px solid #e9ecef !important;
  background: #ffffff !important;
  display: flex;
  flex-direction: column;
}

.item-card-edit:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
  border-color: #007bff !important;
}

.item-card-edit .dentes-badges {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.item-card-edit .dentes-badges .badge-sm {
  font-size: 0.7rem;
  padding: 0.35rem 0.5rem;
  min-width: 32px;
  text-align: center;
  border-radius: 6px;
}

.item-card-edit .item-info {
  flex-grow: 1;
}

/* Cards originais (manter compatibilidade) */
.orcamento-itens-view-section .item-card {
  transition: all 0.2s ease;
  border: 1px solid #e9ecef;
}

.orcamento-itens-view-section .item-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  border-color: #007bff;
}

/* Resumo unificado */
.unified-summary {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border: 1px solid #e9ecef !important;
}

.summary-item {
  padding: 0.5rem;
  transition: all 0.2s ease;
}

.summary-item:hover {
  transform: translateY(-2px);
}

.summary-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  color: white;
  font-weight: bold;
  font-size: 1rem;
  margin: 0 auto;
}

.summary-label {
  font-size: 0.8rem;
  color: #6c757d;
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.summary-value {
  font-size: 0.9rem;
  font-weight: 600;
}

.valores-resumo {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 1px solid #dee2e6;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.valores-resumo hr {
  border-top: 2px solid #dee2e6;
  opacity: 0.5;
  margin: 0.75rem 0;
}

/* Dropdown customizado */
.custom-dropdown {
  position: relative;
  display: inline-block;
  z-index: 10000; /* Garantir que fique acima de tudo */
}

.dropdown-toggle-custom {
  border: 1px solid #dee2e6;
  background: white;
  transition: all 0.2s ease;
}

.dropdown-toggle-custom:hover {
  background: #f8f9fa;
  border-color: #007bff;
  transform: scale(1.05);
}

.custom-dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  z-index: 9999;
  min-width: 220px;
  background: white;
  border: none;
  border-radius: 12px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
  padding: 0.75rem 0;
  margin-top: 0.5rem;
  animation: dropdownFadeIn 0.2s ease-out;
}

@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.custom-dropdown-menu .dropdown-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 1.25rem;
  font-size: 0.9rem;
  color: #495057;
  text-decoration: none;
  border: none;
  background: none;
  width: 100%;
  text-align: left;
  transition: all 0.2s ease;
  cursor: pointer;
}

.custom-dropdown-menu .dropdown-item:hover {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  color: #007bff;
  transform: translateX(8px);
}

.custom-dropdown-menu .dropdown-item.text-danger:hover {
  background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
  color: #e53e3e;
}

.custom-dropdown-menu .dropdown-item i {
  width: 20px;
  text-align: center;
  margin-right: 0.5rem;
}

.custom-dropdown-menu .dropdown-divider {
  height: 1px;
  background: linear-gradient(90deg, transparent, #e9ecef, transparent);
  border: none;
  margin: 0.5rem 1rem;
}
</style>
