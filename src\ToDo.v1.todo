Gerência dos planos:
    ✔ Alterar "meses gratuitos" para "dias gratuitos"
    ✔ Adicionar toggle para selecionar se o plano estará na tela do auto-registro
    ☐ Remover mentorias dos planos
Agenda:
    ✔ Consertar bug do toggle
    ☐ Consertar bug de alterar nome do paciente e não alterar na consulta (talvez podemos remover o nome do paciente do objeto consulta, e manter só o vínculo com o objeto Paciente)
Orçamentos:
    ✔ Validar o funcionamento da adição/gerência do orçamento
    ✔ Validar o funcionamento da adição/gerência dos itens
    ☐ Validar o funcionamento da adição/gerência dos valores
    ☐ Validar o funcionamento dos status
☐ Financeiro (CRUD simples)








Migrate:
    Consultórios:
        ☐ Criar padrão para todos e mover consultas para ele? - fazer via script?
        ✔ Permitir consultório não configurado sem bugar o sistema (config de fallback?)
    Tabela Users atual:
        ☐ Marcar todos como "profissional" - pois agora tem a modalidade de Colaborador



Testar:
    ☐ Tela inicial / abertura da Agenda (com aviso de que a Agenda ainda não foi configurada - link para configurar)
    ☐ Auto-registro
    Pacientes:    
        ☐ Criação de pacientes
        ☐ Preenchimento do perfil/contato/endereço
        ☐ Ficha de avaliação inicial
        ☐ Respostas da ficha (e )

    ☐ Configuração da agenda
    ☐ Exibição da agenda













































SE SOBRAR TEMPO:
    ☐ Fazer landing page


Finalizar financeiro/orçamento (Manter modals comentados - para usar depois):
    ☐ Manter status RASCUNHO só para orçamentos sem itens
    ☐ Criar status pendente para orçamentos com itens pendentes
    ☐ Criar status aprovado para orçamentos com itens

Agenda:
    ☐ Permitir configurar Agenda (por cadeira/consultório e por dia) @started(25-10-19 23:31)
    Melhorar visualização da agenda:
        ☐ Permitir alternar/somar entre agendas de diferentes consultórios (configurados nas configurações da agenda)
☐ Fazer visualização das metas terapêuticas
☐ Gerar atestados médicos
☐ CORRIGIR: painel de serviços do orçamento não mostra todos os serviços
Reunião na Modus (26/09/2025):
    ☐ Deixar verdes as janelas das análises concluídas
    ☐ Colocar opção de ir direto pro Financeiro/Tratamento quando não for ortodontista
POST-DEPLOYMENT:
    ☐ Incluir os planos atuais que são "6 meses grátis", "12 meses grátis" e "vitalício", e gerar os do MindMeister
☐ Verificar telas muito "não-responsivas"
☐ Otimizar carregamento das telas
☐ Armazenar miniatura das imagens (se atentar ao formato das imagens iniciais (3x4, 4x3 etc))
Implementar armazenamento no Backblaze:
    ✔ GET via Worker da Cloudflare
    ☐ POST via API (utilizar URL assinada única para cada upload)
    ☐ Padronizar caminhos: /clinicas/{clinica_id}/pacientes/{paciente_id}/imagens/{nome_da_imagem}
    ☐ Fazer script para "converter" o padrão/local das imagens existentes para o Backblaze
    ☐ Fazer script para replicar dados para o host3 (ou utilizar alternativa de sincronização)


Reunião 27/10/2025:
    Orçamentos:
        ☐ Permitir manter "aprovado parcialmente" - aprovar outros itens futuramente
    Plano de tratamento:
        ☐ Aparelho utilizado: alterar para "ortodontia fixa"
        ☐ Aparelho utilizado: colocar opção "Outros"
        ☐ Miofuncional: deixar campo aberto para digitar
        ☐ Consultas e histórico:
        ☐ Remover "howMuchTime" das datas da timeline
        ☐ Queixa principal: inserir no planejamento (apenas modo ortodontia)
    ☐ Permitir o usuário cadastrar radiologias (entrar no nosso painel para gerência)
    ☐ Permitir radiologia enviar exames (exceto tomografia - arquivo pesado)






✔ Ao editar um orçamento, não tá salvando a edição do item do orçamento @done
✔ Ao editar um orçamento, não tá adicionando novos itens @done
✔ Juntar modo de adição com modo de editar @done

















☐ Fazer uma espécie de camada de "pre-upload" das informações do app (tipo: adicionar um paciente, e ele já concluir instantaneamente, porém enfileirar um "job" para processamento - o front-end fica escutando em segundo plano para ver se o job foi concluído) - pensar em fazer isso também com o upload de imagens (criar uma telinha de "upload" temporária, talvez - like a browser)






☐ Otimizar espaços
v1:
    Melhorar a análise:
    ☐ Verificar outro nome para a aba "Tratamento" (talvez nomear como Tratamento a aba da análise - se encontrar um nome melhor para a aba que vai conter as consultas+histórico+imagens)
    ☐ Gerar cartas de encaminhamento a partir das Necessidades de Encaminhamento
    ☐ Sistema de tickets
    ☐ Verificar: scope com usuários que ainda não logaram
Agenda:
    ✔ Permitir configurar o horário de início e fim da agenda
    ✔ Permitir configurar os dias da semana em que a agenda está aberta
    ✔ Permitir configurar a duração padrão das consultas
    ✔ Permitir configurar a duração de cada consulta (personalizar ao criar)
✔ Fazer sistema de planos para os ortodontistas
✔ Sistema de recuperação de senhas
✔ Corrigir bug de entrar no paciente errado (getPacienteUrl não tá funcionando?)
✔ Colocar todas as imagens na análise full screen (pensar numa forma de encaixar tudo e ficar fácil de visualizar - sem sumir a ficha lateral)
✔ Consertar loading das faturas que parou de funcionar
✔ Melhorar layout do PacienteFinanceiro (status, inputs, retirar datas etc)
✔ Melhorar opções do questionário inicial: tirar checkbox "Nenhum" das perguntas que forem de checkboxes
✔ Implementar GlobalScope no Laravel, para isolar informações das clínicas (detalhes no ChatGPT)
✔ Corrigir edição do histórico
✔ Corrigir envio de imagem de sorriso: girando 90º após upload (?)
✔ Melhorar TabNavigation
Correções e melhorias pós-início (30/09/2025):
    Para superadmins do sistema:
        ✔ Ocultar pacientes de outras clínicas (exibir somente em mentorias e nas listas de pacientes dos ortodontistas assinantes)
        ✔ Ocultar ortodontistas de outras clínicas (exibir somente na gerência dos assinantes)
        ✔ Corrigir link para pacientes que sejam de outras clínicas
    ✔ Verificar Financeiro que está com erro ao carregar
    ✔ Melhorar layout da tela inicial
    ✔ Remover tipo "Serviço" e campo "Valor máximo" da tabela de preços
    ✔ Corrigir: imagens da documentação inicial não estão visíveis na mentoria
    ✔ Preencher e-mails dos usuários
    ✔ Passar paciente id_ficha 34 do Daniel para o usuário e clínica da Gilmara

Reunião na Modus (26/09/2025):
    ✔ Melhorar visualização das análises
    ✔ Retirar imagens dos fatores clínicos do diagnóstico
    ✔ Encaminhamentos: adicionar fonoaudiólogo + otorrinolaringologista
    ✔ Histórico simples > Mudar para "Anotação"
    ✔ Colocar mensagem de aviso sobre a análise automática ("não nos responsabilizamos")
    ✔ Corrigir bug de quando tenta adicionar um histórico e ainda não há consultas na agenda
    ☐ Deixar verdes as janelas das análises concluídas
    ☐ Colocar opção de ir direto pro Financeiro/Tratamento quando não for ortodontista






































v2:
    ☐ Conferir sistema de orçamentos (clinicorp)
    ☐ Aba "Planejamento" e "Tratamento": quando não tiver o módulo para ortodontistas, vamos retirar a "Planejamento" e alterar "Tratamento" para "Histórico" ou "Consultas"
    ☐ Afinar mais ainda a navbar dos steps (talvez diminuir mais o ícone/padding-y?)
    Financeiro a receber:
        ☐ Implementar odontograma
    ☐ CORRIGIR: verificar a expiração do token no front-end, em vez de aguardar a resposta do servidor
    Melhorias de layout:
        Agenda (tela inicial! - tem que ser robusta):
            ☐ Fazer um calendário robusto no canto superior direito (talvez aumentar a largura da sidenav para esta tela)
        ☐ Adicionar texto "Iniciar" ao botão Play das análises
        ☐ Deixar mais elegante o estilo da seta de voltar do paciente para /pacientes  
        ☐ Melhorar layout dos campos de início do tratamento e meses previstos
        Visualização das imagens:
            ☐ Fazer espécie de "lupa" - e talvez aumentar o tamanho da imagem base na tela
            ☐ Tirar "X" de excluir as imagens do efeito hover -  fazer botão pequeno só quando ela tiver aberta em fullscreen
        Badge Paciente/Tratamento/"próxima consulta":
            ☐ Fazer ficar verdinha e com ícone de calendar-check quando já tiver marcada
            ☐ Fazer ficar default e com ícone calendar-day (sem check) quando não tiver marcada
    Logs do Laravel:
        ☐ Traduzir
        ☐ Nos updates, salvar apenas os campos que foram alterados (isso gera a possibilidade de implementarmos um sistema de rollback das alterações - que seja "travado" de forma que seja possível restaurar apenas o último passo - verificar se é melhor apenas excluir a alteração anterior, ou registrar uma nova versão - seguir lógica do git??)

    ☐ Fazer sistema robusto de busca para pacientes e para ortodontistas
    Fazer sistema de cobrança da mensalidade dos ortodontistas:
        ☐ Sistema interno do app para cobrança da própria mensalidade dos usuários (verificar se por segurança é obrigatório fazer isso em outro painel - um app dedicado a admins)
    ☐ Fazer função de reagendar consultas
    ☐ Sistema de tickets
    ☐ Passar as metas terapêuticas para a parte do tratamento
    ☐ Tratamento (planejamento): remover os 4 quadrantes de metas terapêuticas e deixar só a opção para adicionar (e também "depende de outra área")
    Dentista > Pacientes:
        ☐ Exibir listagem de pacientes (com busca simples):
            ☐ Ao clicar, envia para a tela do Paciente
    Dentista > Consultas:
        ☐ Exibir listagem de consultas (com filtro simples)
    ☐ V3: Fazer dashboards analíticos de BI
-- ---------------------------------------------------------
-- ---------------------------------------------------------






✔ Wiki
✔ Sistema de notificações
✔ Revisar protocolos de tratamento
Melhorias de layout:
    ✔ Melhorar layout da tela de análise (a versão full screen)
    Lumi SideNav:
        ✔ Melhorar fonte (deixar mais bold - fica bem melhor para ler)
        ✔ Melhorar efeitos hover dos botões (fazer animação bacana e sutil/elegante?)
✔ Tirar botão de excluir dos grupos diários de imagens
✔ Fazer menu de notificações
-- ---------------------------------------------------------
✔ Notificação de mentoria solicitada
✔ Resposta do mentor no menu de notificações
✔ Fazer opção para adicionar um histórico direto na tela do histórico:
    ✔ Verificar se precisa alterar o model


☐ Criar testes automatizados para a API
☐ Criar versão dev do site (com tela chique de autenticação - "administração")
☐ Fazer carousel ficar "sticky" (preso ao topo quando rolar a tela)
☐ Consultas: criar uma aba para o histórico de consultas - nela aparecem os estados das metas terapêuticas (e permite adicionar?)
☐ Remover observações do tratamento recomendado
Header do tratamento:
    ☐ Manter o header fixo ao rolar a lista de consultas
Funcionalidades:
    ☐ Fazer filtro na agenda para alternar entre "Exibir canceladas"
    ☐ Dentista > Pacientes: marcar os que estiverem com mentoria solicitada
    ☐ Criar lista de funcionalidades do sistema
-- ----------------------------------------------------------------------------------------------------
Later?:
    ☐ Refinar layout para telas entre md e lg? ou telas lg? (na faixa de 768 a 1024p de largura)
    ☐ Adicionar opção para gerar carta de encaminhamento (PDF)
    ☐ Implementar solicitações de reagendamentos (para os funcionários receberem e reagendarem)
    ☐ Fazer último paciente acessado ficar no state global do app, para retornar à ele quando alternar entre as abas
    ☐ Opção para excluir/arquivar paciente
    ☐ Opção para excluir/arquivar ortodontista

