<template>
  <Teleport to="body">
    <div class="modal fade lumi-fade" :id="modalId" tabindex="-1" aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered" style="max-width: 600px">
        <div class="modal-content">
        <div class="modal-header border-0 pb-0">
          <div class="modal-title-container">
            <h5 class="modal-title">
              <i class="fas fa-user-plus text-primary me-2"></i>
              Criar Conta de Teste
            </h5>
            <p class="modal-subtitle mb-0">
              Preencha os dados para começar seu teste gratuito
            </p>
          </div>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>

        <div class="modal-body">
          <!-- Plano Selecionado -->
          <div class="selected-plan-summary mb-4">
            <h6 class="section-title">
              <i class="fas fa-check-circle text-success me-2"></i>
              Plano Selecionado
            </h6>
            <div class="plan-summary-card">
              <div class="d-flex align-items-center">
                <div 
                  class="plan-color-indicator me-3" 
                  :style="{ backgroundColor: planoSelecionado?.cor }"
                ></div>
                <div>
                  <h6 class="mb-1">{{ planoSelecionado?.nome }}</h6>
                  <p class="mb-0 text-muted small">
                    <i class="fas fa-gift me-1"></i>
                    {{ planoSelecionado?.dias_gratuitos || 30 }} dias gratuitos
                  </p>
                </div>
              </div>
            </div>
          </div>

          <!-- Formulário -->
          <form @submit.prevent="submitTrialSignup">
            <!-- Dados da Clínica -->
            <div class="form-section mb-4">
              <h6 class="section-title">
                <i class="fas fa-hospital-building text-primary me-2"></i>
                Dados da Clínica
              </h6>
              
              <div class="mb-3">
                <label class="form-label">Nome da Clínica *</label>
                <MaterialInput
                  v-model="form.clinica.nome"
                  placeholder="Ex: Clínica Odontológica XYZ"
                  :isRequired="true"
                  :input="capitalizeAll"
                />
              </div>
            </div>

            <!-- Dados do Dentista -->
            <div class="form-section">
              <h6 class="section-title">
                <i class="fas fa-user-md text-success me-2"></i>
                Dados do Profissional
              </h6>
              
              <div class="mb-3">
                <label class="form-label">Nome do Dentista *</label>
                <MaterialInput
                  v-model="form.dentista.nome"
                  placeholder="Ex: Dr. João Silva"
                  :isRequired="true"
                  :input="capitalizeAll"
                />
              </div>

              <div class="mb-3">
                <label class="form-label">E-mail *</label>
                <MaterialInput
                  type="email"
                  v-model="form.dentista.email"
                  placeholder="<EMAIL>"
                  :isRequired="true"
                />
              </div>

              <div class="mb-3">
                <label class="form-label">Senha *</label>
                <div class="input-group">
                  <input
                    :type="showPassword ? 'text' : 'password'"
                    class="form-control"
                    v-model="form.dentista.senha"
                    style="background-color: #ffffff;"
                    @input="validatePassword"
                  />
                  <button
                    class="btn btn-outline-secondary"
                    type="button"
                    @click="generatePassword"
                    title="Gerar nova senha"
                  >
                    <i class="fas fa-refresh"></i>
                  </button>
                  <button
                    class="btn btn-outline-secondary"
                    type="button"
                    @click="showPassword = !showPassword"
                    title="Mostrar/ocultar senha"
                  >
                    <i :class="showPassword ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
                  </button>
                </div>
                <small class="text-muted">
                  Mínimo de 8 caracteres. Esta senha será usada para acessar o sistema.
                </small>
              </div>
            </div>
          </form>
        </div>

        <div class="modal-footer border-0 pt-0">
          <button type="button" class="btn btn-secondary" @click="voltarParaSelecaoPlano">
            <i class="fas fa-arrow-left me-2"></i>
            Voltar
          </button>
          <button 
            type="button" 
            class="btn btn-primary"
            :disabled="!isFormValid || isSubmitting"
            @click="submitTrialSignup"
          >
            <span v-if="!isSubmitting">
              <i class="fas fa-rocket me-2"></i>
              Criar Conta e Começar Teste
            </span>
            <span v-else>
              <i class="fas fa-spinner fa-spin me-2"></i>
              Criando conta...
            </span>
          </button>
        </div>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script>
import MaterialInput from '@/components/MaterialInput.vue';
import cSwal from '@/utils/cSwal';
import { capitalizeAll } from '@/helpers/utils';

export default {
  name: 'TrialSignupModal',
  components: {
    MaterialInput
  },
  props: {
    modalId: {
      type: String,
      default: 'trialSignupModal'
    },
    planoSelecionado: {
      type: Object,
      required: true
    }
  },
  emits: ['signup-success', 'signup-error', 'voltar'],
  data() {
    return {
      showPassword: false,
      isSubmitting: false,
      form: {
        clinica: {
          nome: ''
        },
        dentista: {
          nome: '',
          email: '',
          senha: ''
        }
      }
    };
  },
  computed: {
    isFormValid() {
      return (
        this.form.clinica.nome.trim() &&
        this.form.dentista.nome.trim() &&
        this.form.dentista.email.trim() &&
        this.isValidEmail(this.form.dentista.email) &&
        this.form.dentista.senha.length >= 8
      );
    }
  },
  mounted() {
    this.generatePassword();
    
    // Limpar formulário quando o modal for fechado
    const modalElement = document.getElementById(this.modalId);
    if (modalElement) {
      modalElement.addEventListener('hidden.bs.modal', () => {
        this.resetForm();
      });
    }
  },
  methods: {
    capitalizeAll,
    
    generatePassword() {
      const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
      let password = "";
      for (let i = 0; i < 8; i++) {
        password += charset.charAt(Math.floor(Math.random() * charset.length));
      }
      this.form.dentista.senha = password;
    },
    
    validatePassword() {
      // Validação em tempo real se necessário
    },
    
    isValidEmail(email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return emailRegex.test(email);
    },
    
    voltarParaSelecaoPlano() {
      this.$emit('voltar');
    },
    
    resetForm() {
      this.form = {
        clinica: {
          nome: ''
        },
        dentista: {
          nome: '',
          email: '',
          senha: ''
        }
      };
      this.showPassword = false;
      this.isSubmitting = false;
    },
    
    async submitTrialSignup() {
      if (!this.isFormValid || this.isSubmitting) return;

      this.isSubmitting = true;

      try {
        const trialData = {
          plano_id: this.planoSelecionado.id,
          clinica: this.form.clinica,
          dentista: this.form.dentista
        };

        // Emitir evento para o componente pai processar
        this.$emit('signup-success', trialData);

      } catch (error) {
        console.error('Erro no signup trial:', error);
        this.$emit('signup-error', error);
        cSwal.cError(error.message || 'Erro ao criar conta de teste');
        this.isSubmitting = false;
      }
    }
  }
};
</script>

<style scoped>
.modal-title-container {
  flex: 1;
}

.modal-subtitle {
  font-size: 0.875rem;
  color: #6c757d;
  font-weight: normal;
}

.section-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: #495057;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e9ecef;
}

.selected-plan-summary {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1rem;
}

.plan-summary-card {
  background: #ffffff;
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid #e9ecef;
}

.plan-color-indicator {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  flex-shrink: 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.form-section {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
}

.form-label {
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.input-group {
  display: flex;
}

.input-group .form-control {
  border-radius: 8px 0 0 8px;
  border-right: none;
  height: 48px;
  font-size: 15px;
  border: 1.5px solid #e9ecef;
  transition: all 0.3s ease;
}

.input-group .form-control:focus {
  border-color: #56809F;
  box-shadow: 0 0 0 2px rgba(86, 128, 159, 0.1);
  outline: none;
}

.input-group .btn {
  border-radius: 0;
  border: 1.5px solid #e9ecef;
  border-left: none;
  padding: 0 1rem;
  background: #ffffff;
  color: #6c757d;
  transition: all 0.3s ease;
}

.input-group .btn:last-child {
  border-radius: 0 8px 8px 0;
}

.input-group .btn:hover {
  background: #f8f9fa;
  color: #56809F;
}

.btn {
  border-radius: 8px;
  font-weight: 500;
  padding: 0.625rem 1.25rem;
  transition: all 0.3s ease;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary:not(:disabled):hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

/* Responsividade */
@media (max-width: 768px) {
  .form-section {
    padding: 1rem;
  }
  
  .input-group .form-control {
    height: 46px;
    font-size: 16px; /* Evita zoom no iOS */
  }
}
</style>
